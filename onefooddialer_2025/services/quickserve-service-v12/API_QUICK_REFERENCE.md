# API Quick Reference Card

## 🎯 **Mobile App APIs - Complete Order Journey**

### **1. Create Pre-Order (Required)**
```http
POST http://*************:8003/api/v2/order-management/create
Content-Type: application/json

{
  "customer_id": 3800,
  "customer_name": "Customer User",
  "customer_email": "<EMAIL>",
  "customer_phone": "919998887779",
  "customer_address": "123 Test Street, Test Area, Test City - 400001",
  "location_code": 1001,
  "location_name": "Test Location",
  "city": 1,
  "city_name": "Mumbai",
  "product_code": 342,
  "product_name": "International Breakfast Subscription",
  "product_type": "Meal",
  "quantity": 1,
  "amount": 200.00,
  "days_preference": "1,2,3,4,5",
  "delivery_time": "08:00:00",
  "delivery_end_time": "09:00:00",
  "food_preference": "veg",
  "subscription_days": 15,
  "meal_items": [
    {
      "product_code": 343,
      "product_name": "Breakfast Main Course",
      "quantity": 1,
      "amount": 120.00
    }
  ]
}
```

**Response:** Returns `payment_service_transaction_id` for payment processing

### **2. Process Payment (Your Existing APIs)**
```http
# Use your existing Payment Service v12 APIs:
POST http://*************:8002/api/v2/payments/{transaction_id}/process
POST http://*************:8002/api/v2/payment/callback
```

### **3. Check Order Status (Optional)**
```http
GET http://*************:8003/api/v2/order-management/pre-order-status/{order_no}
```

**Response shows:**
- Pre-order details
- Payment status
- Orders created count (0 = pending, 12+ = completed)

## 📋 **Required Fields**

### **Customer Information:**
- `customer_id` (integer)
- `customer_name` (string, max 45)
- `customer_email` (email, max 45)
- `customer_phone` (string, max 15)
- `customer_address` (string, max 250)

### **Location Information:**
- `location_code` (integer)
- `location_name` (string, max 45)
- `city` (integer)
- `city_name` (string, max 45)

### **Product Information:**
- `product_code` (integer)
- `product_name` (string, max 255)
- `product_type` (string, max 60)
- `quantity` (integer, min 1)
- `amount` (number, min 0)

### **Subscription Information:**
- `days_preference` (string) - "1,2,3,4,5" (0=Sunday, 1=Monday, etc.)
- `subscription_days` (integer, 1-30) - Total subscription duration
- `meal_items` (array) - List of meal components

### **Meal Items (Array):**
- `product_code` (integer)
- `product_name` (string, max 255)
- `quantity` (integer, min 1)
- `amount` (number, min 0)

## 🔄 **Order Flow States**

### **Pre-Order Status:**
- `pending` → Payment not completed
- `success` → Payment completed, orders created

### **Payment Status:**
- `initiated` → Payment transaction created
- `completed` → Payment successful
- `failed` → Payment failed

### **Orders Created:**
- `count: 0, status: pending` → Waiting for payment
- `count: 12, status: completed` → Orders created successfully

## 📊 **Example Results**

### **15-Day Subscription (Weekdays Only):**
- **Input:** `subscription_days: 15`, `days_preference: "1,2,3,4,5"`
- **Output:** 12 orders created (weekdays only in 15-day period)
- **Order Details:** 36 records (12 orders × 3 meal items)

### **Order Naming Convention:**
- **Pre-Order:** `ORD202507221240574531`
- **Daily Orders:** `ORD202507221240574531_20250722`, `ORD202507221240574531_20250723`, etc.

## 🚨 **Error Handling**

### **Common Errors:**
- `400` - Invalid request data (missing required fields)
- `404` - Pre-order not found
- `500` - Server error (database transaction failed)

### **Payment Failures:**
- Pre-order remains in temp tables
- Can retry payment with same transaction ID
- Orders not created until payment succeeds

## 🎯 **Success Indicators**

### **Pre-Order Created Successfully:**
```json
{
  "success": true,
  "data": {
    "temp_pre_order_id": 46154,
    "payment_service_transaction_id": "TXN30610"
  }
}
```

### **Orders Created Successfully:**
```json
{
  "orders_created": {
    "count": 12,
    "order_details_count": 36,
    "status": "completed"
  }
}
```

## 🔧 **Testing URLs**

### **Development:**
- **QuickServe:** `http://*************:8003`
- **Payment Service:** `http://*************:8002`

### **Production:**
- **QuickServe:** `https://quickserve.yourdomain.com`
- **Payment Service:** `https://payment.yourdomain.com`

## 📱 **Mobile App Checklist**

- ✅ Collect all required customer and product information
- ✅ Call pre-order creation API
- ✅ Extract `payment_service_transaction_id` from response
- ✅ Use existing Payment Service APIs for payment processing
- ✅ Optionally poll pre-order status for UI updates
- ✅ Show success when `orders_created.status` = "completed"

**That's it! Your complete order journey in 3 API calls.** 🚀
