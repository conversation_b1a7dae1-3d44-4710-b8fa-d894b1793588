# Order Management API Flow Explanation

## 🎯 **Single Service Architecture - No Multiple API Calls Required**

**IMPORTANT**: The order management system is designed as a **single service architecture**. Once you create an order and payment is processed, **NO additional API calls to other services are required** to complete the order.

## 📋 **Complete Order Flow**

### **Step 1: Create Order**
```http
POST /api/v2/order-management/create
```

**What happens automatically:**
1. ✅ Order record created in `orders` table
2. ✅ Order details created in `order_details` table (meal items)
3. ✅ Payment transaction created in `payment_transaction` table
4. ✅ Order status set to "New"
5. ✅ Payment transaction status set to "pending"

**Response includes:**
- Order ID and Order Number
- Transaction ID for payment tracking
- Payment URL for status checking

### **Step 2: Payment Processing (Automatic)**
```http
POST /api/v2/payment/webhook
```

**What happens automatically when payment is confirmed:**
1. ✅ Payment gateway sends webhook to our system
2. ✅ Order status automatically updated to "Confirmed"
3. ✅ Payment transaction status updated to "completed"
4. ✅ `amount_paid` field set to 1 (indicating payment received)
5. ✅ **Recurring orders automatically created** based on `days_preference`
6. ✅ Future orders scheduled for delivery dates

**No manual intervention required!**

### **Step 3: Order Completion (Automatic)**

**The order is now COMPLETE and ready for fulfillment!**

- ✅ Order status: "Confirmed"
- ✅ Payment: Received
- ✅ Recurring orders: Created for future dates
- ✅ Ready for kitchen/delivery processing

## 🔄 **Do You Need Multiple API Calls?**

### **Answer: NO! Here's why:**

1. **Single Service Design**: Everything happens within the QuickServe service
2. **Automatic Processing**: Payment confirmation triggers all necessary updates
3. **No External Dependencies**: No calls to separate payment, inventory, or fulfillment services needed
4. **Complete Integration**: All order lifecycle management in one place

## 📊 **API Usage Patterns**

### **For Mobile Apps/Frontend:**

#### **Basic Order Flow (Minimum Required):**
```javascript
// 1. Create order (REQUIRED)
const orderResponse = await fetch('/api/v2/order-management/create', {
  method: 'POST',
  body: JSON.stringify(orderData)
});

// 2. Redirect user to payment gateway
// Payment gateway handles payment and sends webhook automatically

// 3. Check order status (OPTIONAL - for UI updates)
const statusResponse = await fetch(`/api/v2/payment/status/${orderNo}`);

// That's it! Order is complete when payment is confirmed.
```

#### **Enhanced Order Flow (With Status Tracking):**
```javascript
// 1. Create order
const order = await createOrder(orderData);

// 2. Poll payment status (optional - for real-time UI updates)
const checkPaymentStatus = async () => {
  const status = await fetch(`/api/v2/payment/status/${order.order_no}`);
  const data = await status.json();
  
  if (data.data.order_status === 'Confirmed') {
    // Order is complete! Show success message
    showOrderConfirmation(data);
  } else if (data.data.order_status === 'Payment Failed') {
    // Handle payment failure
    showPaymentError(data);
  }
};

// 3. Get customer orders (for order history)
const customerOrders = await fetch(`/api/v2/order-management/customer/${customerId}`);
```

## 🎛️ **Optional API Calls (For Enhanced Features)**

These are **optional** calls for enhanced user experience:

### **Order Tracking:**
- `GET /api/v2/payment/status/{orderNo}` - Check payment/order status
- `GET /api/v2/order-management/details/{orderNo}` - Get full order details
- `GET /api/v2/order-management/customer/{customerId}` - Get customer order history

### **Testing/Development:**
- `POST /api/v2/payment/simulate/success` - Test payment success
- `POST /api/v2/payment/simulate/failure` - Test payment failure

## 🔧 **Integration Examples**

### **React/JavaScript Frontend:**
```javascript
class OrderService {
  async createOrder(orderData) {
    const response = await fetch('/api/v2/order-management/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(orderData)
    });
    return response.json();
  }

  async getOrderStatus(orderNo) {
    const response = await fetch(`/api/v2/payment/status/${orderNo}`);
    return response.json();
  }

  async getCustomerOrders(customerId, page = 1) {
    const response = await fetch(`/api/v2/order-management/customer/${customerId}?per_page=10&page=${page}`);
    return response.json();
  }
}
```

### **Flutter/Mobile App:**
```dart
class OrderService {
  Future<Map<String, dynamic>> createOrder(Map<String, dynamic> orderData) async {
    final response = await http.post(
      Uri.parse('$baseUrl/order-management/create'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(orderData),
    );
    return jsonDecode(response.body);
  }

  Future<Map<String, dynamic>> getOrderStatus(String orderNo) async {
    final response = await http.get(
      Uri.parse('$baseUrl/payment/status/$orderNo'),
    );
    return jsonDecode(response.body);
  }
}
```

## 🚀 **Production Deployment Considerations**

### **Webhook Security:**
- Implement signature verification for payment webhooks
- Use HTTPS for all API endpoints
- Validate webhook source IP addresses

### **Error Handling:**
- Implement retry logic for failed webhook processing
- Set up monitoring for payment confirmation delays
- Handle duplicate webhook notifications

### **Performance:**
- Use database indexing on `order_no` and `customer_code`
- Implement caching for frequently accessed order data
- Consider async processing for recurring order creation

## 📈 **Monitoring & Analytics**

### **Key Metrics to Track:**
- Order creation success rate
- Payment confirmation time
- Webhook processing success rate
- Recurring order generation accuracy

### **Logging:**
- All payment webhooks received
- Order status transitions
- Failed payment attempts
- Recurring order creation events

## 🎯 **Summary**

**You only need ONE API call to create a complete order:**
1. `POST /api/v2/order-management/create` - Creates order and sets up payment
2. Payment gateway handles payment processing via webhooks
3. Order automatically becomes "Confirmed" when payment succeeds
4. Recurring orders automatically created for subscriptions

**Additional API calls are optional** and only needed for:
- Real-time status updates in UI
- Order history display
- Enhanced user experience features

The system is designed to be **simple, automatic, and complete** with minimal API interactions required.
