# Order Management API Flow Explanation

## 🎯 **Updated Architecture - Integration with Payment Service v12**

**IMPORTANT**: The order management system now integrates with your existing **Payment Service v12**. The flow uses your 3 payment APIs while keeping order management in QuickServe.

## 📋 **Your Mobile App Payment APIs**
1. **POST /api/v2/payments** - Initiate payment (Payment Service)
2. **POST /api/v2/payments/{id}/process** - Process with gateway (Payment Service)
3. **POST /api/v2/payment/callback** - Handle callback (Payment Service)

## 📋 **Updated Complete Order Flow**

### **Step 1: Create Order (QuickServe)**
```http
POST /api/v2/order-management/create (QuickServe Service)
```

**What happens automatically:**
1. ✅ Order record created in `orders` table
2. ✅ Order details created in `order_details` table (meal items)
3. ✅ Local payment transaction created for tracking
4. ✅ **Calls Payment Service v12** to initiate payment
5. ✅ Order status set to "New"
6. ✅ Returns payment URLs for mobile app

**Response includes:**
- Order ID and Order Number
- Local transaction ID (QuickServe tracking)
- Payment Service transaction ID
- **Payment URLs for your mobile app**

### **Step 2: Payment Processing (Your Mobile App)**
```http
# Your existing mobile app flow:
POST /api/v2/payments/{id}/process (Payment Service)
POST /api/v2/payment/callback (Payment Service)
```

**Your mobile app continues using your existing 3 APIs:**
1. ✅ Use payment service transaction ID from Step 1
2. ✅ Process payment with your existing flow
3. ✅ Payment service handles gateway communication

### **Step 3: Payment Confirmation (Automatic)**
```http
POST /api/v2/order-management/payment-success/{orderNo} (Payment Service calls QuickServe)
```

**When payment succeeds, Payment Service calls QuickServe:**
1. ✅ Order status automatically updated to "Confirmed"
2. ✅ Payment transaction status updated to "completed"
3. ✅ `amount_paid` field set to 1 (indicating payment received)
4. ✅ **Recurring orders automatically created** based on `days_preference`
5. ✅ Future orders scheduled for delivery dates

**No manual intervention required!**

### **Step 4: Order Completion (Automatic)**

**The order is now COMPLETE and ready for fulfillment!**

- ✅ Order status: "Confirmed"
- ✅ Payment: Received via Payment Service
- ✅ Recurring orders: Created for future dates
- ✅ Ready for kitchen/delivery processing

## 🔄 **Do You Need Multiple API Calls?**

### **Answer: NO! Your existing mobile app flow works perfectly:**

1. **Order Creation**: One call to QuickServe creates order + initiates payment
2. **Payment Processing**: Your existing 3 Payment Service APIs work unchanged
3. **Automatic Completion**: Payment Service automatically notifies QuickServe on success/failure
4. **No Additional Calls**: Order becomes "Confirmed" automatically

## 📊 **API Usage Patterns**

### **For Mobile Apps/Frontend:**

#### **Updated Mobile App Flow (Using Your Existing Payment APIs):**
```javascript
// 1. Create order with QuickServe (NEW - replaces your current order creation)
const orderResponse = await fetch('http://quickserve:8003/api/v2/order-management/create', {
  method: 'POST',
  body: JSON.stringify(orderData)
});

const { payment_service_transaction_id, payment_urls } = orderResponse.data;

// 2. Use your existing Payment Service APIs (UNCHANGED)
const processResponse = await fetch(`http://payment:8002/api/v2/payments/${payment_service_transaction_id}/process`, {
  method: 'POST',
  body: JSON.stringify(paymentData)
});

// 3. Handle payment callback with your existing flow (UNCHANGED)
// Payment Service automatically calls QuickServe on success/failure

// 4. Check order status (OPTIONAL - for UI updates)
const orderStatus = await fetch(`${payment_urls.order_status}`);

// That's it! Order is automatically confirmed when payment succeeds.
```

#### **Enhanced Order Flow (With Status Tracking):**
```javascript
// 1. Create order
const order = await createOrder(orderData);

// 2. Poll payment status (optional - for real-time UI updates)
const checkPaymentStatus = async () => {
  const status = await fetch(`/api/v2/payment/status/${order.order_no}`);
  const data = await status.json();
  
  if (data.data.order_status === 'Confirmed') {
    // Order is complete! Show success message
    showOrderConfirmation(data);
  } else if (data.data.order_status === 'Payment Failed') {
    // Handle payment failure
    showPaymentError(data);
  }
};

// 3. Get customer orders (for order history)
const customerOrders = await fetch(`/api/v2/order-management/customer/${customerId}`);
```

## 🎛️ **Optional API Calls (For Enhanced Features)**

These are **optional** calls for enhanced user experience:

### **Order Tracking:**
- `GET /api/v2/payment/status/{orderNo}` - Check payment/order status
- `GET /api/v2/order-management/details/{orderNo}` - Get full order details
- `GET /api/v2/order-management/customer/{customerId}` - Get customer order history

### **Testing/Development:**
- `POST /api/v2/payment/simulate/success` - Test payment success
- `POST /api/v2/payment/simulate/failure` - Test payment failure

## 🔧 **Integration Examples**

### **React/JavaScript Frontend:**
```javascript
class OrderService {
  async createOrder(orderData) {
    const response = await fetch('/api/v2/order-management/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(orderData)
    });
    return response.json();
  }

  async getOrderStatus(orderNo) {
    const response = await fetch(`/api/v2/payment/status/${orderNo}`);
    return response.json();
  }

  async getCustomerOrders(customerId, page = 1) {
    const response = await fetch(`/api/v2/order-management/customer/${customerId}?per_page=10&page=${page}`);
    return response.json();
  }
}
```

### **Flutter/Mobile App:**
```dart
class OrderService {
  Future<Map<String, dynamic>> createOrder(Map<String, dynamic> orderData) async {
    final response = await http.post(
      Uri.parse('$baseUrl/order-management/create'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(orderData),
    );
    return jsonDecode(response.body);
  }

  Future<Map<String, dynamic>> getOrderStatus(String orderNo) async {
    final response = await http.get(
      Uri.parse('$baseUrl/payment/status/$orderNo'),
    );
    return jsonDecode(response.body);
  }
}
```

## 🚀 **Production Deployment Considerations**

### **Webhook Security:**
- Implement signature verification for payment webhooks
- Use HTTPS for all API endpoints
- Validate webhook source IP addresses

### **Error Handling:**
- Implement retry logic for failed webhook processing
- Set up monitoring for payment confirmation delays
- Handle duplicate webhook notifications

### **Performance:**
- Use database indexing on `order_no` and `customer_code`
- Implement caching for frequently accessed order data
- Consider async processing for recurring order creation

## 📈 **Monitoring & Analytics**

### **Key Metrics to Track:**
- Order creation success rate
- Payment confirmation time
- Webhook processing success rate
- Recurring order generation accuracy

### **Logging:**
- All payment webhooks received
- Order status transitions
- Failed payment attempts
- Recurring order creation events

## 🎯 **Summary**

**You only need ONE API call to create a complete order:**
1. `POST /api/v2/order-management/create` - Creates order and sets up payment
2. Payment gateway handles payment processing via webhooks
3. Order automatically becomes "Confirmed" when payment succeeds
4. Recurring orders automatically created for subscriptions

**Additional API calls are optional** and only needed for:
- Real-time status updates in UI
- Order history display
- Enhanced user experience features

The system is designed to be **simple, automatic, and complete** with minimal API interactions required.
