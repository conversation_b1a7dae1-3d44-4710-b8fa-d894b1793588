<?php

/**
 * Direct Order Test - Bypass Payment Service to test order creation directly
 * This will help identify if the issue is in Payment Service or Order Management
 */

echo "=== DIRECT ORDER TEST (BYPASS PAYMENT SERVICE) ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

$startTime = microtime(true);
$pdo = new PDO('mysql:host=127.0.0.1;dbname=live_quickserve_8163', 'root', 'Automation@321');

// Generate unique order number
$orderNo = 'TEST' . date('ymdHis');
echo "Generated Order Number: $orderNo\n\n";

// Step 1: Create temp_pre_order manually
echo "Step 1: Creating temp_pre_order...\n";
$tempPreOrderSql = "
INSERT INTO temp_pre_orders (
    company_id, unit_id, fk_kitchen_code, ref_order, order_no, auth_id, customer_code, customer_name,
    food_preference, phone, email_address, location_code, location_name, city, city_name,
    product_code, product_name, product_description, product_type, quantity, order_days,
    product_price, amount, total_amt, tax, total_tax, delivery_charges, service_charges,
    total_delivery_charges, line_delivery_charges, applied_discount, total_applied_discount,
    order_status, order_date, due_date, ship_address, order_menu, invoice_status, amount_paid,
    inventory_type, food_type, total_third_party_charges, order_for, PRODUCT_MEAL_CALENDAR,
    delivery_type, payment_mode, days_preference, source, delivery_time, delivery_end_time, recurring_status
) VALUES (
    8163, 8163, 1, 0, ?, 27802, 27802, 'Test Customer',
    'veg', '9999999999', '<EMAIL>', 1001, 'Test Location', 1, 'Mumbai',
    342, 'Test Breakfast', 'Test Breakfast', 'Meal', 1, ?,
    78.00, 78.00, 78.00, 0.00, 0.00, 0.00, 0.00,
    0.00, 0.00, 0.00, 0.00,
    'New', ?, ?, 'Test Address', 'meal', 'Unbill', 0,
    'perishable', 'veg', 0.00, 'fixed', 1,
    'delivery', NULL, '1,2,3,4,5', 'api', '08:00:00', '09:00:00', '1'
)";

$orderDays = json_encode([date('Y-m-d'), date('Y-m-d', strtotime('+1 day')), date('Y-m-d', strtotime('+2 days'))]);
$orderDate = date('Y-m-d');

$stmt = $pdo->prepare($tempPreOrderSql);
$result = $stmt->execute([$orderNo, $orderDays, $orderDate, $orderDate]);

if (!$result) {
    echo "ERROR: Failed to create temp_pre_order\n";
    print_r($stmt->errorInfo());
    exit(1);
}

$tempPreOrderId = $pdo->lastInsertId();
echo "✅ temp_pre_order created (ID: $tempPreOrderId)\n";

// Step 2: Create temp_order_payment
echo "Step 2: Creating temp_order_payment...\n";
$tempPaymentSql = "
INSERT INTO temp_order_payment (
    company_id, unit_id, temp_order_id, temp_preorder_id, amount, status, date, type, istodaysorder, order_menu, recurring_status
) VALUES (
    8163, 8163, 0, ?, 78.00, 'pending', ?, 'online', '0', 'meal', '1'
)";

$stmt = $pdo->prepare($tempPaymentSql);
$result = $stmt->execute([$tempPreOrderId, $orderDate]);

if (!$result) {
    echo "ERROR: Failed to create temp_order_payment\n";
    print_r($stmt->errorInfo());
    exit(1);
}

echo "✅ temp_order_payment created\n";

// Step 3: Create payment_transaction
echo "Step 3: Creating payment_transaction...\n";
$paymentTransactionSql = "
INSERT INTO payment_transaction (
    company_id, unit_id, customer_id, customer_email, customer_phone, customer_name,
    payment_amount, transaction_charges, wallet_amount, pre_order_id, gateway, status,
    gateway_transaction_id, description, created_date, transaction_by, referer,
    success_url, failure_url, context, discount, recurring
) VALUES (
    8163, 8163, 27802, '<EMAIL>', '9999999999', 'Test Customer',
    78.00, 2.34, 0.00, ?, 'initiated', 'initiated',
    ?, 'Payment initiated for Test Breakfast', NOW(), 'api', 'order_api',
    ?, ?, 'order_payment', 0.00, 1
)";

$transactionId = 'TXN_' . time();
$successUrl = "http://*************:8003/api/v2/order-management/payment-success/$orderNo";
$failureUrl = "http://*************:8003/api/v2/order-management/payment-failure/$orderNo";

$stmt = $pdo->prepare($paymentTransactionSql);
$result = $stmt->execute([$orderNo, $transactionId, $successUrl, $failureUrl]);

if (!$result) {
    echo "ERROR: Failed to create payment_transaction\n";
    print_r($stmt->errorInfo());
    exit(1);
}

$paymentTransactionPkId = $pdo->lastInsertId();
echo "✅ payment_transaction created (ID: $paymentTransactionPkId)\n";

$setupTime = microtime(true) - $startTime;
echo "Setup completed in: " . round($setupTime, 2) . " seconds\n\n";

// Step 4: Test Payment Success API directly
echo "Step 4: Testing Payment Success API...\n";
$paymentData = [
    'payment_service_transaction_id' => $transactionId,
    'gateway' => 'razorpay',
    'amount' => 78.00
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://*************:8003/api/v2/order-management/payment-success/$orderNo");
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);

$paymentResponse = curl_exec($ch);
$paymentHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

$paymentTime = microtime(true) - $startTime;
echo "Payment API call took: " . round($paymentTime - $setupTime, 2) . " seconds\n";

if ($curlError) {
    echo "ERROR: cURL error: $curlError\n";
    exit(1);
}

if ($paymentHttpCode !== 200) {
    echo "ERROR: Payment API failed with HTTP code: $paymentHttpCode\n";
    echo "Response: $paymentResponse\n";
    exit(1);
}

$paymentResult = json_decode($paymentResponse, true);
if (!$paymentResult['success']) {
    echo "ERROR: Payment processing failed: " . $paymentResult['message'] . "\n";
    exit(1);
}

echo "✅ Payment Success API completed successfully\n";
echo "✅ Orders created: " . $paymentResult['data']['created_orders_count'] . "\n";
echo "✅ Order details created: " . $paymentResult['data']['created_order_details_count'] . "\n";

// Step 5: Verify all tables
echo "\nStep 5: Verifying all tables...\n";

// Check orders
$stmt = $pdo->prepare("SELECT COUNT(*) as count, order_status FROM orders WHERE order_no = ? GROUP BY order_status");
$stmt->execute([$orderNo]);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
$totalOrders = array_sum(array_column($orders, 'count'));
echo "✅ orders: $totalOrders records\n";
foreach ($orders as $order) {
    echo "   - Status: {$order['order_status']} (Count: {$order['count']})\n";
}

// Check order_details
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM order_details WHERE ref_order_no = ?");
$stmt->execute([$orderNo]);
$orderDetails = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ order_details: {$orderDetails['count']} records\n";

// Check payment_transaction status
$stmt = $pdo->prepare("SELECT COUNT(*) as count, status FROM payment_transaction WHERE pre_order_id = ? GROUP BY status");
$stmt->execute([$orderNo]);
$paymentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "✅ payment_transaction: " . count($paymentTransactions) . " statuses\n";
foreach ($paymentTransactions as $pt) {
    echo "   - Status: {$pt['status']} (Count: {$pt['count']})\n";
}

// Check temp_order_payment status
$stmt = $pdo->prepare("SELECT status FROM temp_order_payment WHERE temp_preorder_id = ?");
$stmt->execute([$tempPreOrderId]);
$tempPayment = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ temp_order_payment: Status: {$tempPayment['status']}\n";

// Check payment_transfered
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM payment_transfered pt JOIN payment_transaction ptx ON pt.fk_transaction_id = ptx.pk_transaction_id WHERE ptx.pre_order_id = ?");
$stmt->execute([$orderNo]);
$paymentTransferred = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ payment_transfered: {$paymentTransferred['count']} records\n";

$totalTime = microtime(true) - $startTime;
echo "\n=== DIRECT TEST COMPLETED ===\n";
echo "Total time: " . round($totalTime, 2) . " seconds\n";
echo "Status: " . ($totalTime < 30 ? "✅ WITHIN TIME LIMIT" : "❌ EXCEEDED TIME LIMIT") . "\n";

echo "\n=== SUMMARY ===\n";
echo "Order Number: $orderNo\n";
echo "Transaction ID: $transactionId\n";
echo "Orders Created: $totalOrders\n";
echo "Order Details Created: {$orderDetails['count']}\n";
echo "Payment Status: " . implode(', ', array_column($paymentTransactions, 'status')) . "\n";
echo "Temp Payment Status: {$tempPayment['status']}\n";
echo "All tables updated successfully!\n";

if ($totalTime < 30) {
    echo "\n✅ SUCCESS: Order creation and payment processing completed within time limit!\n";
    echo "The issue is likely in the Payment Service, not the Order Management logic.\n";
} else {
    echo "\n❌ WARNING: Process took too long - may cause timeout issues.\n";
}
