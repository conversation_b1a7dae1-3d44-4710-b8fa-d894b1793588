<?php

/**
 * Test Order Flow - Complete order placement and payment verification
 * Tests all table updates within 30 seconds to avoid max execution time
 */

echo "=== TESTING COMPLETE ORDER FLOW ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

// Step 1: Create Order
echo "Step 1: Creating Order...\n";
$startTime = microtime(true);

$orderData = [
    'customer_id' => 27802,
    'customer_name' => 'Test Customer',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '9999999999',
    'customer_address' => 'Test Address',
    'location_code' => 1001,
    'location_name' => 'Test Location',
    'city' => 1,
    'city_name' => 'Mumbai',
    'product_code' => 342,
    'product_name' => 'Test Breakfast',
    'product_type' => 'Meal',
    'quantity' => 1,
    'amount' => 78.00,
    'days_preference' => '1,2,3,4,5',
    'delivery_time' => '08:00:00',
    'delivery_end_time' => '09:00:00',
    'food_preference' => 'veg',
    'subscription_days' => 3,
    'meal_items' => [
        [
            'product_code' => 343,
            'product_name' => 'Main Course',
            'quantity' => 1,
            'amount' => 50.00
        ],
        [
            'product_code' => 344,
            'product_name' => 'Side Dish',
            'quantity' => 1,
            'amount' => 28.00
        ]
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://*************:8003/api/v2/order-management/create');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 25);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$createTime = microtime(true) - $startTime;
echo "Order creation took: " . round($createTime, 2) . " seconds\n";

if ($httpCode !== 200) {
    echo "ERROR: Order creation failed with HTTP code: $httpCode\n";
    echo "Response: $response\n";
    exit(1);
}

$orderResponse = json_decode($response, true);
if (!$orderResponse['success']) {
    echo "ERROR: Order creation failed: " . $orderResponse['message'] . "\n";
    exit(1);
}

$orderNo = $orderResponse['data']['order_no'];
$transactionId = $orderResponse['data']['payment_service_transaction_id'];
echo "✅ Order created successfully: $orderNo\n";
echo "✅ Transaction ID: $transactionId\n";
echo "Time elapsed: " . round(microtime(true) - $startTime, 2) . " seconds\n\n";

// Step 2: Verify Initial Tables
echo "Step 2: Verifying Initial Tables...\n";
$pdo = new PDO('mysql:host=127.0.0.1;dbname=live_quickserve_8163', 'root', 'Automation@321');

// Check temp_pre_orders
$stmt = $pdo->prepare("SELECT pk_order_no, order_no, customer_name, amount, order_status FROM temp_pre_orders WHERE order_no = ?");
$stmt->execute([$orderNo]);
$tempPreOrder = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ temp_pre_orders: " . ($tempPreOrder ? "Created (ID: {$tempPreOrder['pk_order_no']})" : "NOT FOUND") . "\n";

// Check temp_order_payment
$stmt = $pdo->prepare("SELECT status FROM temp_order_payment WHERE temp_preorder_id = ?");
$stmt->execute([$tempPreOrder['pk_order_no']]);
$tempPayment = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ temp_order_payment: " . ($tempPayment ? "Status: {$tempPayment['status']}" : "NOT FOUND") . "\n";

// Check payment_transaction
$stmt = $pdo->prepare("SELECT COUNT(*) as count, status FROM payment_transaction WHERE pre_order_id = ? GROUP BY status");
$stmt->execute([$orderNo]);
$paymentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "✅ payment_transaction: " . count($paymentTransactions) . " records\n";
foreach ($paymentTransactions as $pt) {
    echo "   - Status: {$pt['status']} (Count: {$pt['count']})\n";
}

echo "Time elapsed: " . round(microtime(true) - $startTime, 2) . " seconds\n\n";

// Step 3: Process Payment Success
echo "Step 3: Processing Payment Success...\n";
$paymentData = [
    'payment_service_transaction_id' => $transactionId,
    'gateway' => 'razorpay',
    'amount' => 78.00
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://*************:8003/api/v2/order-management/payment-success/$orderNo");
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 20);

$paymentResponse = curl_exec($ch);
$paymentHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$paymentTime = microtime(true) - $startTime;
echo "Payment processing took: " . round($paymentTime - $createTime, 2) . " seconds\n";

if ($paymentHttpCode !== 200) {
    echo "ERROR: Payment processing failed with HTTP code: $paymentHttpCode\n";
    echo "Response: $paymentResponse\n";
    exit(1);
}

$paymentResult = json_decode($paymentResponse, true);
if (!$paymentResult['success']) {
    echo "ERROR: Payment processing failed: " . $paymentResult['message'] . "\n";
    exit(1);
}

echo "✅ Payment processed successfully\n";
echo "✅ Orders created: " . $paymentResult['data']['created_orders_count'] . "\n";
echo "✅ Order details created: " . $paymentResult['data']['created_order_details_count'] . "\n";
echo "Time elapsed: " . round(microtime(true) - $startTime, 2) . " seconds\n\n";

// Step 4: Verify Final Tables
echo "Step 4: Verifying Final Tables...\n";

// Check orders table
$stmt = $pdo->prepare("SELECT COUNT(*) as count, order_status FROM orders WHERE order_no = ? GROUP BY order_status");
$stmt->execute([$orderNo]);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "✅ orders: " . array_sum(array_column($orders, 'count')) . " records\n";
foreach ($orders as $order) {
    echo "   - Status: {$order['order_status']} (Count: {$order['count']})\n";
}

// Check order_details table
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM order_details WHERE ref_order_no = ?");
$stmt->execute([$orderNo]);
$orderDetails = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ order_details: {$orderDetails['count']} records\n";

// Check updated payment_transaction
$stmt = $pdo->prepare("SELECT COUNT(*) as count, status FROM payment_transaction WHERE pre_order_id = ? GROUP BY status");
$stmt->execute([$orderNo]);
$finalPaymentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "✅ payment_transaction (final): " . count($finalPaymentTransactions) . " statuses\n";
foreach ($finalPaymentTransactions as $pt) {
    echo "   - Status: {$pt['status']} (Count: {$pt['count']})\n";
}

// Check updated temp_order_payment
$stmt = $pdo->prepare("SELECT status FROM temp_order_payment WHERE temp_preorder_id = ?");
$stmt->execute([$tempPreOrder['pk_order_no']]);
$finalTempPayment = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ temp_order_payment (final): Status: {$finalTempPayment['status']}\n";

// Check payment_transfered
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM payment_transfered pt JOIN payment_transaction ptx ON pt.fk_transaction_id = ptx.pk_transaction_id WHERE ptx.pre_order_id = ?");
$stmt->execute([$orderNo]);
$paymentTransferred = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ payment_transfered: {$paymentTransferred['count']} records\n";

$totalTime = microtime(true) - $startTime;
echo "\n=== FLOW COMPLETED ===\n";
echo "Total time: " . round($totalTime, 2) . " seconds\n";
echo "Status: " . ($totalTime < 30 ? "✅ WITHIN TIME LIMIT" : "❌ EXCEEDED TIME LIMIT") . "\n";

if ($totalTime >= 30) {
    echo "WARNING: Flow took longer than 30 seconds - may cause max execution time issues\n";
}

echo "\n=== SUMMARY ===\n";
echo "Order Number: $orderNo\n";
echo "Transaction ID: $transactionId\n";
echo "Orders Created: " . $paymentResult['data']['created_orders_count'] . "\n";
echo "Order Details Created: " . $paymentResult['data']['created_order_details_count'] . "\n";
echo "All tables updated successfully!\n";
