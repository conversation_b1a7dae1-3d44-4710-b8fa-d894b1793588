<?php

/**
 * Final comprehensive test of the complete order and payment flow
 * Tests within 30 seconds to ensure no timeout issues
 */

echo "=== FINAL COMPREHENSIVE ORDER FLOW TEST ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

$overallStartTime = microtime(true);

// Test 1: Create Order
echo "🧪 Test 1: Creating Order...\n";
$orderStartTime = microtime(true);

$orderData = [
    'customer_id' => 27802,
    'customer_name' => 'Final Test Customer',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '8888888888',
    'customer_address' => 'Final Test Address',
    'location_code' => 1001,
    'location_name' => 'Test Location',
    'city' => 1,
    'city_name' => 'Mumbai',
    'product_code' => 342,
    'product_name' => 'Final Test Breakfast',
    'product_type' => 'Meal',
    'quantity' => 1,
    'amount' => 78.00,
    'days_preference' => '1,2,3,4,5',
    'delivery_time' => '08:00:00',
    'delivery_end_time' => '09:00:00',
    'food_preference' => 'veg',
    'subscription_days' => 3,
    'meal_items' => [
        [
            'product_code' => 343,
            'product_name' => 'Final Main Course',
            'quantity' => 1,
            'amount' => 50.00
        ],
        [
            'product_code' => 344,
            'product_name' => 'Final Side Dish',
            'quantity' => 1,
            'amount' => 28.00
        ]
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://*************:8003/api/v2/order-management/create');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 20);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

$orderTime = microtime(true) - $orderStartTime;
echo "Order creation took: " . round($orderTime, 2) . " seconds\n";

if ($curlError) {
    echo "❌ cURL Error: $curlError\n";
    exit(1);
}

if ($httpCode !== 200 && $httpCode !== 201) {
    echo "❌ HTTP Error: $httpCode\n";
    echo "Response: $response\n";
    exit(1);
}

$orderResponse = json_decode($response, true);
if (!$orderResponse['success']) {
    echo "❌ Order Creation Failed: " . $orderResponse['message'] . "\n";
    exit(1);
}

$orderNo = $orderResponse['data']['order_no'];
$transactionId = $orderResponse['data']['payment_service_transaction_id'] ?? 'N/A';
echo "✅ Order created successfully: $orderNo\n";
echo "✅ Transaction ID: $transactionId\n";

// Test 2: Verify Database Records
echo "\n🧪 Test 2: Verifying Database Records...\n";
$pdo = new PDO('mysql:host=127.0.0.1;dbname=live_quickserve_8163', 'root', 'Automation@321');

// Check temp_pre_orders
$stmt = $pdo->prepare("SELECT pk_order_no, order_no, customer_name, amount, order_status FROM temp_pre_orders WHERE order_no = ?");
$stmt->execute([$orderNo]);
$tempPreOrder = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ temp_pre_orders: " . ($tempPreOrder ? "Found (ID: {$tempPreOrder['pk_order_no']})" : "❌ NOT FOUND") . "\n";

if (!$tempPreOrder) {
    echo "❌ Critical Error: temp_pre_order not found!\n";
    exit(1);
}

// Check temp_order_payment
$stmt = $pdo->prepare("SELECT status FROM temp_order_payment WHERE temp_preorder_id = ?");
$stmt->execute([$tempPreOrder['pk_order_no']]);
$tempPayment = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ temp_order_payment: " . ($tempPayment ? "Status: {$tempPayment['status']}" : "❌ NOT FOUND") . "\n";

// Check payment_transaction
$stmt = $pdo->prepare("SELECT COUNT(*) as count, status FROM payment_transaction WHERE pre_order_id = ? GROUP BY status");
$stmt->execute([$orderNo]);
$paymentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "✅ payment_transaction: " . count($paymentTransactions) . " status groups\n";
foreach ($paymentTransactions as $pt) {
    echo "   - Status: {$pt['status']} (Count: {$pt['count']})\n";
}

// Test 3: Process Payment Success
echo "\n🧪 Test 3: Processing Payment Success...\n";
$paymentStartTime = microtime(true);

$paymentData = [
    'payment_service_transaction_id' => $transactionId,
    'gateway' => 'razorpay',
    'amount' => 78.00
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://*************:8003/api/v2/order-management/payment-success/$orderNo");
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);

$paymentResponse = curl_exec($ch);
$paymentHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$paymentCurlError = curl_error($ch);
curl_close($ch);

$paymentTime = microtime(true) - $paymentStartTime;
echo "Payment processing took: " . round($paymentTime, 2) . " seconds\n";

if ($paymentCurlError) {
    echo "❌ Payment cURL Error: $paymentCurlError\n";
    exit(1);
}

if ($paymentHttpCode !== 200) {
    echo "❌ Payment HTTP Error: $paymentHttpCode\n";
    echo "Response: $paymentResponse\n";
    exit(1);
}

$paymentResult = json_decode($paymentResponse, true);
if (!$paymentResult['success']) {
    echo "❌ Payment Processing Failed: " . $paymentResult['message'] . "\n";
    exit(1);
}

echo "✅ Payment processed successfully\n";
echo "✅ Orders created: " . $paymentResult['data']['created_orders_count'] . "\n";
echo "✅ Order details created: " . $paymentResult['data']['created_order_details_count'] . "\n";

// Test 4: Final Database Verification
echo "\n🧪 Test 4: Final Database Verification...\n";

// Check orders table
$stmt = $pdo->prepare("SELECT COUNT(*) as count, order_status FROM orders WHERE order_no = ? GROUP BY order_status");
$stmt->execute([$orderNo]);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
$totalOrders = array_sum(array_column($orders, 'count'));
echo "✅ orders: $totalOrders records\n";
foreach ($orders as $order) {
    echo "   - Status: {$order['order_status']} (Count: {$order['count']})\n";
}

// Check order_details table
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM order_details WHERE ref_order_no = ?");
$stmt->execute([$orderNo]);
$orderDetails = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ order_details: {$orderDetails['count']} records\n";

// Check final payment status
$stmt = $pdo->prepare("SELECT COUNT(*) as count, status FROM payment_transaction WHERE pre_order_id = ? GROUP BY status");
$stmt->execute([$orderNo]);
$finalPaymentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "✅ payment_transaction (final): " . count($finalPaymentTransactions) . " status groups\n";
foreach ($finalPaymentTransactions as $pt) {
    echo "   - Status: {$pt['status']} (Count: {$pt['count']})\n";
}

// Check temp_order_payment final status
$stmt = $pdo->prepare("SELECT status FROM temp_order_payment WHERE temp_preorder_id = ?");
$stmt->execute([$tempPreOrder['pk_order_no']]);
$finalTempPayment = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ temp_order_payment (final): Status: {$finalTempPayment['status']}\n";

// Check payment_transfered
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM payment_transfered pt JOIN payment_transaction ptx ON pt.fk_transaction_id = ptx.pk_transaction_id WHERE ptx.pre_order_id = ?");
$stmt->execute([$orderNo]);
$paymentTransferred = $stmt->fetch(PDO::FETCH_ASSOC);
echo "✅ payment_transfered: {$paymentTransferred['count']} records\n";

$totalTime = microtime(true) - $overallStartTime;
echo "\n=== FINAL TEST RESULTS ===\n";
echo "Order Number: $orderNo\n";
echo "Transaction ID: $transactionId\n";
echo "Total Time: " . round($totalTime, 2) . " seconds\n";
echo "Order Creation Time: " . round($orderTime, 2) . " seconds\n";
echo "Payment Processing Time: " . round($paymentTime, 2) . " seconds\n";

// Final Status
if ($totalTime < 30) {
    echo "\n🎉 SUCCESS: Complete flow completed within 30 seconds!\n";
    echo "✅ All tables updated correctly\n";
    echo "✅ Orders created: $totalOrders\n";
    echo "✅ Order details created: {$orderDetails['count']}\n";
    echo "✅ Payment status: " . implode(', ', array_column($finalPaymentTransactions, 'status')) . "\n";
    echo "✅ Temp payment status: {$finalTempPayment['status']}\n";
    echo "\n🚀 The order and payment flow is working perfectly!\n";
} else {
    echo "\n⚠️  WARNING: Flow took longer than 30 seconds\n";
    echo "This may cause timeout issues in production\n";
}

echo "\n=== TEST COMPLETED ===\n";
