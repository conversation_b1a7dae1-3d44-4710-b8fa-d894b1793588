<?php
/**
 * Test script to verify the fixed order placement flow
 * Tests: Single order creation, proper tax calculation, due_date = NULL, no duplicates
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 Testing Fixed Order Placement Flow\n";
echo "=====================================\n\n";

$startTime = microtime(true);

// Test Configuration
$testOrderData = [
    'customer_id' => 27802,
    'customer_name' => 'Test Customer Fixed Flow',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '9999999999',
    'customer_address' => 'Test Address',
    'location_code' => 1001,
    'location_name' => 'Test Location',
    'city' => 1,
    'city_name' => 'Mumbai',
    'product_code' => 339,
    'product_name' => 'Indian Breakfast',
    'product_type' => 'breakfast',
    'quantity' => 1,
    'amount' => 78.00,
    'days_preference' => '1,2,3,4,5',
    'delivery_time' => '08:00:00',
    'delivery_end_time' => '09:00:00',
    'food_preference' => 'veg',
    'subscription_days' => 3
];

try {
    // Step 1: Create Order
    echo "Step 1: Creating Order...\n";
    $response = file_get_contents('http://************:8003/api/v2/order-management/create', false, stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => json_encode($testOrderData),
            'timeout' => 30
        ]
    ]));

    if ($response === false) {
        throw new Exception('Failed to create order - no response');
    }

    $orderResponse = json_decode($response, true);
    
    if (!$orderResponse || !$orderResponse['success']) {
        throw new Exception('Order creation failed: ' . ($orderResponse['message'] ?? 'Unknown error'));
    }

    $orderNo = $orderResponse['data']['order_no'];
    $transactionId = $orderResponse['data']['transaction_id'];
    
    echo "✅ Order created successfully\n";
    echo "   Order No: {$orderNo}\n";
    echo "   Transaction ID: {$transactionId}\n\n";

    // Step 2: Process Payment Success
    echo "Step 2: Processing Payment Success...\n";
    $paymentData = [
        'payment_service_transaction_id' => $transactionId,
        'gateway' => 'razorpay',
        'amount' => 78.00
    ];

    $paymentResponse = file_get_contents("http://************:8003/api/v2/order-management/payment-success/{$orderNo}", false, stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => json_encode($paymentData),
            'timeout' => 30
        ]
    ]));

    if ($paymentResponse === false) {
        throw new Exception('Payment processing failed - no response');
    }

    $paymentResult = json_decode($paymentResponse, true);
    
    if (!$paymentResult || !$paymentResult['success']) {
        throw new Exception('Payment processing failed: ' . ($paymentResult['message'] ?? 'Unknown error'));
    }

    echo "✅ Payment processed successfully\n\n";

    // Step 3: Verify Results
    echo "Step 3: Verifying Order Results...\n";
    
    // Check orders table
    $orders = DB::table('orders')->where('order_no', $orderNo)->get();
    echo "📊 Orders created: " . count($orders) . "\n";
    
    if (count($orders) > 0) {
        foreach ($orders as $order) {
            echo "   - Order ID: {$order->pk_order_no}\n";
            echo "   - Amount: {$order->amount}\n";
            echo "   - Tax: {$order->tax}\n";
            echo "   - Due Date: " . ($order->due_date ?? 'NULL') . " ✅\n";
            echo "   - Order Date: {$order->order_date}\n";
            echo "   - Status: {$order->order_status}\n\n";
        }
    }

    // Check order_details table
    $orderDetails = DB::table('order_details')->where('ref_order_no', $orderNo)->get();
    echo "📊 Order Details created: " . count($orderDetails) . "\n";
    
    if (count($orderDetails) > 0) {
        foreach ($orderDetails as $detail) {
            echo "   - Product: {$detail->product_name}\n";
            echo "   - Amount: {$detail->product_amount}\n";
            echo "   - Tax: {$detail->product_tax}\n";
            echo "   - Quantity: {$detail->quantity}\n\n";
        }
    }

    // Check for duplicates
    $duplicateOrders = DB::table('orders')
        ->where('order_no', $orderNo)
        ->groupBy('order_no')
        ->havingRaw('COUNT(*) > ?', [count(json_decode($testOrderData['days_preference'] ?? '[]', true))])
        ->count();

    if ($duplicateOrders > 0) {
        echo "❌ DUPLICATE ORDERS DETECTED!\n";
    } else {
        echo "✅ No duplicate orders found\n";
    }

    // Verify tax calculation
    $totalTaxExpected = 78.00 * 0.05; // 5% GST
    $actualTax = $orders->sum('tax');
    
    echo "💰 Tax Verification:\n";
    echo "   Expected Tax (5%): " . number_format($totalTaxExpected, 2) . "\n";
    echo "   Actual Tax: " . number_format($actualTax, 2) . "\n";
    
    if (abs($actualTax - $totalTaxExpected) < 0.01) {
        echo "✅ Tax calculation correct\n";
    } else {
        echo "❌ Tax calculation incorrect\n";
    }

    echo "\n🎉 Test completed successfully!\n";
    echo "Time elapsed: " . round(microtime(true) - $startTime, 2) . " seconds\n";

} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Time elapsed: " . round(microtime(true) - $startTime, 2) . " seconds\n";
    exit(1);
}
