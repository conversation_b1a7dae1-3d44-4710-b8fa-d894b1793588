<?php

/**
 * Fix the failed order 4BRW250723 by manually completing the order creation
 * Since payment was successful but callback failed due to Razorpay timeout
 */

echo "=== FIXING FAILED ORDER 4BRW250723 ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

$orderNo = '4BRW250723';
$tempPreOrderId = 46213;

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=live_quickserve_8163', 'root', 'Automation@321');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Step 1: Checking current status...\n";
    
    // Check temp_pre_order
    $stmt = $pdo->prepare("SELECT * FROM temp_pre_orders WHERE order_no = ?");
    $stmt->execute([$orderNo]);
    $tempPreOrder = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tempPreOrder) {
        echo "❌ Error: temp_pre_order not found!\n";
        exit(1);
    }
    
    echo "✅ temp_pre_order found: {$tempPreOrder['customer_name']}, Amount: {$tempPreOrder['amount']}\n";
    
    // Check payment status
    $stmt = $pdo->prepare("SELECT * FROM payment_transaction WHERE pre_order_id = ?");
    $stmt->execute([$orderNo]);
    $paymentTransaction = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$paymentTransaction || $paymentTransaction['status'] !== 'completed') {
        echo "❌ Error: Payment not completed! Status: " . ($paymentTransaction['status'] ?? 'NOT FOUND') . "\n";
        exit(1);
    }
    
    echo "✅ Payment transaction completed: {$paymentTransaction['gateway']}\n";
    
    // Check if orders already exist
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE order_no = ?");
    $stmt->execute([$orderNo]);
    $existingOrders = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($existingOrders > 0) {
        echo "✅ Orders already exist ($existingOrders records). Checking details...\n";
    } else {
        echo "Step 2: Creating missing orders...\n";
        
        // Parse order days
        $orderDays = json_decode($tempPreOrder['order_days'], true);
        if (!$orderDays) {
            $orderDays = [date('Y-m-d')]; // Default to today
        }
        
        $orderDate = $orderDays[0]; // Use first date
        
        // Create single order (following existing pattern)
        $orderSql = "
        INSERT INTO orders (
            company_id, unit_id, fk_kitchen_code, ref_order, order_no, auth_id, customer_code, customer_name,
            food_preference, phone, email_address, location_code, location_name, city, city_name,
            product_code, product_name, product_description, product_type, quantity, order_days,
            product_price, amount, total_amt, tax, total_tax, delivery_charges, service_charges,
            total_delivery_charges, line_delivery_charges, applied_discount, total_applied_discount,
            order_status, order_date, due_date, ship_address, order_menu, invoice_status, amount_paid,
            inventory_type, food_type, total_third_party_charges, order_for, PRODUCT_MEAL_CALENDAR,
            delivery_type, payment_mode, days_preference, source, delivery_time, delivery_end_time, recurring_status
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?,
            'Confirmed', ?, ?, ?, ?, 'Bill', ?,
            ?, ?, ?, ?, ?,
            ?, 'razorpay', ?, 'api', ?, ?, ?
        )";
        
        $stmt = $pdo->prepare($orderSql);
        $result = $stmt->execute([
            $tempPreOrder['company_id'], $tempPreOrder['unit_id'], $tempPreOrder['fk_kitchen_code'], 
            $tempPreOrder['pk_order_no'], $tempPreOrder['order_no'], $tempPreOrder['auth_id'], 
            $tempPreOrder['customer_code'], $tempPreOrder['customer_name'],
            $tempPreOrder['food_preference'], $tempPreOrder['phone'], $tempPreOrder['email_address'], 
            $tempPreOrder['location_code'], $tempPreOrder['location_name'], $tempPreOrder['city'], $tempPreOrder['city_name'],
            $tempPreOrder['product_code'], $tempPreOrder['product_name'], $tempPreOrder['product_description'], 
            $tempPreOrder['product_type'], $tempPreOrder['quantity'], $tempPreOrder['order_days'],
            $tempPreOrder['product_price'], $tempPreOrder['amount'], $tempPreOrder['total_amt'], 
            $tempPreOrder['tax'], $tempPreOrder['total_tax'], $tempPreOrder['delivery_charges'], $tempPreOrder['service_charges'],
            $tempPreOrder['total_delivery_charges'], $tempPreOrder['line_delivery_charges'], 
            $tempPreOrder['applied_discount'], $tempPreOrder['total_applied_discount'],
            $orderDate, $tempPreOrder['due_date'], $tempPreOrder['ship_address'], 
            $tempPreOrder['order_menu'], $tempPreOrder['amount_paid'],
            $tempPreOrder['inventory_type'], $tempPreOrder['food_type'], $tempPreOrder['total_third_party_charges'], 
            $tempPreOrder['order_for'], $tempPreOrder['PRODUCT_MEAL_CALENDAR'],
            $tempPreOrder['delivery_type'], $tempPreOrder['days_preference'], 
            $tempPreOrder['delivery_time'], $tempPreOrder['delivery_end_time'], $tempPreOrder['recurring_status']
        ]);
        
        if (!$result) {
            echo "❌ Error: Failed to create order\n";
            exit(1);
        }
        
        $orderId = $pdo->lastInsertId();
        echo "✅ Order created successfully (ID: $orderId)\n";
        
        // Create order details (meal items)
        $mealItems = [
            ['product_code' => 343, 'product_name' => 'Main Course', 'quantity' => 1, 'amount' => 50.00],
            ['product_code' => 344, 'product_name' => 'Side Dish', 'quantity' => 1, 'amount' => 28.00],
            ['product_code' => 342, 'product_name' => 'Indian Breakfast', 'quantity' => 1, 'amount' => 78.00]
        ];
        
        foreach ($mealItems as $item) {
            $detailSql = "
            INSERT INTO order_details (
                company_id, unit_id, fk_kitchen_code, ref_order_no, product_code, product_name,
                quantity, order_date, product_amount, food_preference, delivery_time, delivery_end_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $pdo->prepare($detailSql);
            $stmt->execute([
                $tempPreOrder['company_id'], $tempPreOrder['unit_id'], $tempPreOrder['fk_kitchen_code'],
                $orderNo, $item['product_code'], $item['product_name'],
                $item['quantity'], $orderDate, $item['amount'],
                $tempPreOrder['food_preference'], $tempPreOrder['delivery_time'], $tempPreOrder['delivery_end_time']
            ]);
        }
        
        echo "✅ Order details created (3 items)\n";
    }
    
    // Step 3: Update temp_order_payment status
    echo "Step 3: Updating temp_order_payment status...\n";
    $stmt = $pdo->prepare("UPDATE temp_order_payment SET status = 'success' WHERE temp_preorder_id = ?");
    $stmt->execute([$tempPreOrderId]);
    echo "✅ temp_order_payment status updated to 'success'\n";
    
    // Step 4: Create payment_transfered record if missing
    echo "Step 4: Creating payment_transfered record...\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM payment_transfered pt JOIN payment_transaction ptx ON pt.fk_transaction_id = ptx.pk_transaction_id WHERE ptx.pre_order_id = ?");
    $stmt->execute([$orderNo]);
    $existingTransfer = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($existingTransfer == 0) {
        $transferSql = "
        INSERT INTO payment_transfered (
            pk_transfer_id, company_id, unit_id, fk_transaction_id, source, recipient,
            amount, currency, amount_reversed, transfered_at, created_date, description
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $transferId = 'TXF_' . time() . '_' . $orderNo;
        $stmt = $pdo->prepare($transferSql);
        $stmt->execute([
            $transferId, $tempPreOrder['company_id'], $tempPreOrder['unit_id'], 
            $paymentTransaction['pk_transaction_id'], 'customer', 'merchant',
            (int)($tempPreOrder['amount'] * 100), 'INR', 0, 
            time(), date('Y-m-d H:i:s'), "Payment transfer for order $orderNo"
        ]);
        
        echo "✅ payment_transfered record created\n";
    } else {
        echo "✅ payment_transfered record already exists\n";
    }
    
    // Step 5: Final verification
    echo "\nStep 5: Final verification...\n";
    
    // Check orders
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE order_no = ?");
    $stmt->execute([$orderNo]);
    $orderCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Check order_details
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM order_details WHERE ref_order_no = ?");
    $stmt->execute([$orderNo]);
    $detailCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Check temp_order_payment
    $stmt = $pdo->prepare("SELECT status FROM temp_order_payment WHERE temp_preorder_id = ?");
    $stmt->execute([$tempPreOrderId]);
    $tempPaymentStatus = $stmt->fetch(PDO::FETCH_ASSOC)['status'];
    
    // Check payment_transfered
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM payment_transfered pt JOIN payment_transaction ptx ON pt.fk_transaction_id = ptx.pk_transaction_id WHERE ptx.pre_order_id = ?");
    $stmt->execute([$orderNo]);
    $transferCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "=== FINAL STATUS ===\n";
    echo "Order Number: $orderNo\n";
    echo "Orders: $orderCount records ✅\n";
    echo "Order Details: $detailCount records ✅\n";
    echo "Payment Transaction: completed ✅\n";
    echo "Temp Order Payment: $tempPaymentStatus ✅\n";
    echo "Payment Transferred: $transferCount records ✅\n";
    
    if ($orderCount > 0 && $detailCount > 0 && $tempPaymentStatus === 'success' && $transferCount > 0) {
        echo "\n🎉 SUCCESS: Order 4BRW250723 has been fully completed!\n";
        echo "All tables are now properly updated.\n";
    } else {
        echo "\n❌ WARNING: Some records may still be missing\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== FIX COMPLETED ===\n";
