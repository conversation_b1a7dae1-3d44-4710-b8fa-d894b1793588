<?php

/**
 * Test Payment Service directly to identify timeout issue
 */

echo "=== TESTING PAYMENT SERVICE DIRECTLY ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

$startTime = microtime(true);

// Test 1: Simple GET request to Payment Service
echo "Test 1: Testing Payment Service connectivity...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://192.168.1.161:8005/api/v2/payments');
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

$connectTime = microtime(true) - $startTime;
echo "Connectivity test took: " . round($connectTime, 2) . " seconds\n";

if ($curlError) {
    echo "ERROR: cURL error: $curlError\n";
    exit(1);
}

if ($httpCode !== 200) {
    echo "ERROR: HTTP code: $httpCode\n";
    echo "Response: $response\n";
    exit(1);
}

echo "✅ Payment Service is accessible\n\n";

// Test 2: Simple POST request with minimal data
echo "Test 2: Testing minimal payment creation...\n";
$minimalData = [
    'customer_id' => 27802,
    'customer_name' => 'Test Customer',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '9999999999',
    'amount' => 78.00,
    'order_id' => 'MINIMAL_TEST_' . time(),
    'success_url' => 'http://test.com/success',
    'failure_url' => 'http://test.com/failure'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://192.168.1.161:8005/api/v2/payments');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($minimalData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$postStartTime = microtime(true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

$postTime = microtime(true) - $postStartTime;
echo "Payment creation took: " . round($postTime, 2) . " seconds\n";

if ($curlError) {
    echo "ERROR: cURL error: $curlError\n";
    echo "This indicates the Payment Service is hanging or taking too long\n";
    exit(1);
}

if ($httpCode !== 200 && $httpCode !== 201) {
    echo "ERROR: HTTP code: $httpCode\n";
    echo "Response: $response\n";
    exit(1);
}

$paymentResponse = json_decode($response, true);
if (!$paymentResponse['success']) {
    echo "ERROR: Payment creation failed: " . $paymentResponse['message'] . "\n";
    exit(1);
}

echo "✅ Payment creation successful\n";
echo "Transaction ID: " . $paymentResponse['data']['transaction_id'] . "\n";

$totalTime = microtime(true) - $startTime;
echo "\n=== PAYMENT SERVICE TEST COMPLETED ===\n";
echo "Total time: " . round($totalTime, 2) . " seconds\n";
echo "Status: " . ($totalTime < 15 ? "✅ ACCEPTABLE" : "❌ TOO SLOW") . "\n";

if ($totalTime >= 15) {
    echo "WARNING: Payment Service is too slow - will cause timeouts in order creation\n";
} else {
    echo "✅ Payment Service is working within acceptable time limits\n";
}
