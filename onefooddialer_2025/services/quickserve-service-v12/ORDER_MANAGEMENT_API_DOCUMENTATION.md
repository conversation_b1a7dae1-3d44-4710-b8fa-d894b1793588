# Order Management API Documentation

## Overview

This document describes the comprehensive order management system implemented in the QuickServe service. The system handles the complete order lifecycle from creation to payment processing and recurring order management.

## Base URL

```
http://*************:8003/api/v2
```

## API Endpoints

### 1. Order Management

#### Create Order
**POST** `/order-management/create`

Creates a new order with meal items and sets up payment transaction.

**Request Body:**
```json
{
  "customer_id": 3800,
  "customer_name": "Customer User",
  "customer_email": "<EMAIL>",
  "customer_phone": "************",
  "customer_address": "123 Test Street, Test Area, Test City - 400001",
  "location_code": 1001,
  "location_name": "Test Location",
  "city": 1,
  "city_name": "Mumbai",
  "product_code": 336,
  "product_name": "Indian Lunch",
  "product_type": "Meal",
  "quantity": 1,
  "amount": 125.00,
  "days_preference": "1,2,3,4,5",
  "delivery_time": "12:30:00",
  "delivery_end_time": "13:30:00",
  "food_preference": "veg",
  "meal_items": [
    {
      "product_code": 341,
      "product_name": "Mix Veg Raita",
      "quantity": 1,
      "amount": 25.00
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Order created successfully",
  "data": {
    "order_id": 127616,
    "order_no": "ORD202507220800028674",
    "customer_id": 3800,
    "amount": 125,
    "status": "New",
    "days_preference": "1,2,3,4,5",
    "transaction_id": 30604,
    "payment_url": "http://localhost:8003/api/v2/payment/status/ORD202507220800028674"
  }
}
```

#### Get Order Details
**GET** `/order-management/details/{orderNo}`

Retrieves complete order information including meal items and payment status.

**Response:**
```json
{
  "success": true,
  "data": {
    "order": {
      "order_id": 127616,
      "order_no": "ORD202507220800028674",
      "customer_id": 3800,
      "customer_name": "Customer User",
      "product_name": "Indian Lunch",
      "amount": "125.00",
      "order_status": "New",
      "days_preference": "1,2,3,4,5"
    },
    "meal_items": [
      {
        "product_code": 341,
        "product_name": "Mix Veg Raita",
        "quantity": 1,
        "amount": "25.00"
      }
    ],
    "payment_transaction": {
      "transaction_id": 30604,
      "status": "pending",
      "gateway": "pending"
    }
  }
}
```

#### Get Customer Orders
**GET** `/order-management/customer/{customerId}?per_page=10&status=New`

Retrieves paginated list of orders for a customer.

**Query Parameters:**
- `per_page` (optional): Number of orders per page (default: 10)
- `status` (optional): Filter by order status

### 2. Payment Management

#### Get Payment Status
**GET** `/payment/status/{orderNo}`

Retrieves payment status for an order.

**Response:**
```json
{
  "success": true,
  "data": {
    "order_no": "ORD202507220800028674",
    "order_status": "Confirmed",
    "amount": "125.00",
    "amount_paid": "1.00",
    "payment_mode": "simulation",
    "payment_transaction": {
      "transaction_id": 30604,
      "gateway_transaction_id": "sim_687f43ddb9c82",
      "status": "completed",
      "gateway": "razorpay"
    }
  }
}
```

#### Payment Webhook
**POST** `/payment/webhook`

Handles payment gateway webhooks for automatic payment processing.

#### Payment Callback
**POST** `/payment/callback`

Handles payment gateway callbacks (redirects).

#### Manual Payment Confirmation
**POST** `/payment/confirm` (Authenticated)

Manually confirm payment (for admin use).

**Request Body:**
```json
{
  "order_no": "ORD202507220800028674",
  "transaction_id": "txn_123456",
  "status": "completed",
  "amount": 125.00,
  "gateway": "razorpay"
}
```

### 3. Testing Endpoints

#### Simulate Payment Success
**POST** `/payment/simulate/success`

Simulates successful payment for testing.

**Request Body:**
```json
{
  "order_no": "ORD202507220800028674"
}
```

#### Simulate Payment Failure
**POST** `/payment/simulate/failure`

Simulates payment failure for testing.

## Database Schema

### Orders Table
- `pk_order_no`: Primary key
- `order_no`: Unique order identifier
- `customer_code`: Customer ID
- `product_code`: Main product code
- `amount`: Order amount
- `order_status`: Order status (New, Confirmed, Scheduled, etc.)
- `payment_mode`: Payment method
- `amount_paid`: Payment status (0/1)
- `days_preference`: Delivery days (e.g., "1,2,3,4,5")
- `recurring_status`: Recurring order flag

### Order Details Table
- `pk_order_detail_id`: Primary key
- `ref_order_no`: Reference to order_no
- `meal_code`: Main meal product code
- `product_code`: Individual item code
- `product_name`: Item name
- `quantity`: Item quantity
- `product_amount`: Item amount

### Payment Transaction Table
- `pk_transaction_id`: Primary key
- `pre_order_id`: Reference to order_no
- `customer_id`: Customer ID
- `payment_amount`: Transaction amount
- `gateway`: Payment gateway
- `status`: Transaction status
- `gateway_transaction_id`: Gateway transaction ID

## Order Flow

1. **Order Creation**: Customer creates order with meal items
2. **Payment Processing**: Payment transaction is created
3. **Payment Confirmation**: Payment gateway confirms payment
4. **Order Update**: Order status updated to "Confirmed"
5. **Recurring Orders**: If recurring, future orders are created based on days_preference
6. **Order Fulfillment**: Orders are processed for delivery

## Days Preference Format

The `days_preference` field uses comma-separated day numbers:
- 0 = Sunday
- 1 = Monday
- 2 = Tuesday
- 3 = Wednesday
- 4 = Thursday
- 5 = Friday
- 6 = Saturday

Example: "1,2,3,4,5" = Monday to Friday

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description"
}
```

Common HTTP status codes:
- 200: Success
- 201: Created
- 400: Bad Request
- 404: Not Found
- 500: Internal Server Error

## Testing

Use the provided test script to verify the complete order flow:

```bash
php test-complete-order-flow.php
```

This script tests:
- Order creation
- Payment processing
- Recurring order generation
- Payment failure scenarios
- Order retrieval

## Security Considerations

- Payment webhooks should implement signature verification in production
- Sensitive payment data should be encrypted
- API rate limiting should be implemented
- Authentication required for admin endpoints

## Integration Notes

- The system integrates with the existing QuickServe database schema
- Payment processing supports multiple gateways (Razorpay, PayU, etc.)
- Recurring orders are automatically generated based on subscription preferences
- The system maintains backward compatibility with existing order management
