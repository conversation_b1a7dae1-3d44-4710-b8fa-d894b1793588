<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

use App\Http\Controllers\Api\V2\BackorderController;
use App\Http\Controllers\Api\V2\ConfigController;
use App\Http\Controllers\Api\V2\CustomerController;
use App\Http\Controllers\HealthController;
use App\Http\Controllers\Api\V2\HealthController as V2HealthController;
use App\Http\Controllers\Api\V2\LocationMappingController;
use App\Http\Controllers\Api\V2\MetricsController;
use App\Http\Controllers\Api\V2\OrderController;
use App\Http\Controllers\Api\V2\PaymentWebhookController;
use App\Http\Controllers\Api\V2\ProductController;
use App\Http\Controllers\Api\V2\SettingsController;
use App\Http\Controllers\Api\V2\TimeslotController;
use App\Http\Controllers\Api\OrderController as ApiOrderController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public Customer Order APIs (no authentication required for testing)
Route::prefix('public/v2/orders')->group(function () {
    Route::get('/customer/{customerId}/categorized', [OrderController::class, 'getCategorizedOrdersByCustomer']);
    Route::get('/customer/{customerId}/current', [OrderController::class, 'getCurrentOrdersByCustomer']);
    Route::get('/customer/{customerId}/cancelled', [OrderController::class, 'getCancelledOrdersByCustomer']);
    Route::get('/customer/{customerId}/past', [OrderController::class, 'getPastOrdersByCustomer']);
    Route::get('/customer/{customerId}', [OrderController::class, 'getByCustomer']);
});

// Payment webhook routes (no authentication required for webhooks)
Route::prefix('v2/payment')->group(function () {
    Route::post('/webhook', [PaymentWebhookController::class, 'handleWebhook']);
    Route::post('/callback', [PaymentWebhookController::class, 'handleCallback']);
    Route::get('/status/{orderNo}', [PaymentWebhookController::class, 'getPaymentStatus']);

    // Testing routes
    Route::post('/simulate/success', [PaymentWebhookController::class, 'simulatePaymentSuccess']);
    Route::post('/simulate/failure', [PaymentWebhookController::class, 'simulatePaymentFailure']);
});

// Payment management routes (authenticated)
Route::prefix('v2/payment')->middleware(['auth:sanctum'])->group(function () {
    Route::post('/confirm', [PaymentWebhookController::class, 'confirmPayment']);
});

// API v2 routes (for the new microservice architecture)
Route::prefix('v2')->group(function () {
    // Public customer order routes (for testing and mobile app integration)
    Route::prefix('orders')->group(function () {
        // Customer order categorization APIs (public for testing)
        Route::get('/customer/{customerId}/categorized', [OrderController::class, 'getCategorizedOrdersByCustomer']);
        Route::get('/customer/{customerId}/current', [OrderController::class, 'getCurrentOrdersByCustomer']);
        Route::get('/customer/{customerId}/cancelled', [OrderController::class, 'getCancelledOrdersByCustomer']);
        Route::get('/customer/{customerId}/past', [OrderController::class, 'getPastOrdersByCustomer']);
        Route::get('/customer/{customerId}', [OrderController::class, 'getByCustomer']);
    });

    // Direct order routes (for frontend integration)
    Route::prefix('orders')->middleware(['auth:sanctum'])->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::post('/', [OrderController::class, 'store']);
        Route::get('/{id}', [OrderController::class, 'show']);
        Route::put('/{id}', [OrderController::class, 'update']);
        Route::delete('/{id}', [OrderController::class, 'destroy']);
        Route::get('/customer/{customerId}', [OrderController::class, 'getByCustomer']);
        Route::get('/customer/{customerId}/categorized', [OrderController::class, 'getCategorizedOrdersByCustomer']);
        Route::get('/customer/{customerId}/current', [OrderController::class, 'getCurrentOrdersByCustomer']);
        Route::get('/customer/{customerId}/cancelled', [OrderController::class, 'getCancelledOrdersByCustomer']);
        Route::get('/customer/{customerId}/past', [OrderController::class, 'getPastOrdersByCustomer']);
        Route::patch('/{id}/status', [OrderController::class, 'updateStatus']);
        Route::patch('/{id}/delivery-status', [OrderController::class, 'updateDeliveryStatus']);
        Route::post('/{id}/cancel', [OrderController::class, 'cancel']);
        Route::post('/{id}/payment', [OrderController::class, 'processPayment']);

        // Additional frontend-expected endpoints
        Route::post('/assign', [OrderController::class, 'assignOrder']);
        Route::post('/pickup', [OrderController::class, 'markPickup']);
        Route::post('/in-transit', [OrderController::class, 'markInTransit']);
        Route::post('/deliver', [OrderController::class, 'markDelivered']);
        Route::post('/fail', [OrderController::class, 'markFailed']);
        Route::post('/notes', [OrderController::class, 'addNote']);
        Route::get('/notes', [OrderController::class, 'getNotes']);
        Route::post('/items', [OrderController::class, 'addItem']);
        Route::put('/items/{itemId}', [OrderController::class, 'updateItem']);
        Route::delete('/items/{itemId}', [OrderController::class, 'removeItem']);

        // Order item specific routes
        Route::get('/{orderId}/items/{itemId}', [OrderController::class, 'getOrderItem']);
        Route::put('/{orderId}/items/{itemId}', [OrderController::class, 'updateOrderItem']);
        Route::delete('/{orderId}/items/{itemId}', [OrderController::class, 'removeOrderItem']);
        Route::post('/refunds', [OrderController::class, 'createRefund']);
        Route::get('/refunds', [OrderController::class, 'getRefunds']);
        Route::get('/payments', [OrderController::class, 'getPayments']);
        Route::post('/invoice', [OrderController::class, 'generateInvoice']);
        Route::post('/send-confirmation', [OrderController::class, 'sendConfirmation']);
        Route::post('/apply-coupon', [OrderController::class, 'applyCoupon']);
        Route::post('/remove-coupon', [OrderController::class, 'removeCoupon']);
        Route::post('/calculate-totals', [OrderController::class, 'calculateTotals']);
        Route::get('/history', [OrderController::class, 'getHistory']);
        Route::get('/statistics', [OrderController::class, 'getStatistics']);
        Route::get('/route', [OrderController::class, 'getRoute']);
        Route::post('/number/{orderNumber}', [OrderController::class, 'getByOrderNumber']);
        Route::post('/start-preparation', [OrderController::class, 'startPreparation']);
        Route::post('/ready', [OrderController::class, 'markReady']);
        Route::post('/complete', [OrderController::class, 'markComplete']);
        Route::get('/invoice', [OrderController::class, 'getInvoice']);
        Route::get('/search', [OrderController::class, 'search']);
    });

    // QuickServe routes
    Route::prefix('quickserve')->group(function () {
        // Health check and metrics
        Route::get('health', [V2HealthController::class, 'index']);
        Route::get('health/detailed', [HealthController::class, 'detailed'])->middleware('auth:sanctum');
        Route::get('metrics', [MetricsController::class, 'export'])->middleware('auth:sanctum');

        // Order routes
        Route::prefix('orders')->group(function () {
            Route::get('/', [OrderController::class, 'index']);
            Route::post('/', [OrderController::class, 'store']);
            Route::get('/{id}', [OrderController::class, 'show']);
            Route::put('/{id}', [OrderController::class, 'update']);
            Route::delete('/{id}', [OrderController::class, 'destroy']);
            Route::get('/customer/{customerId}', [OrderController::class, 'getByCustomer']);
            Route::patch('/{id}/status', [OrderController::class, 'updateStatus']);
            Route::patch('/{id}/delivery-status', [OrderController::class, 'updateDeliveryStatus']);
            Route::post('/{id}/cancel', [OrderController::class, 'cancel']);
            Route::post('/{id}/payment', [OrderController::class, 'processPayment']);
            Route::get('/{id}/payment/success', function (Request $request, $id) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment successful',
                    'order_id' => $id,
                    'transaction_id' => $request->query('transaction_id'),
                ]);
            })->name('payment.success');
            Route::get('/{id}/payment/failure', function (Request $request, $id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment failed',
                    'order_id' => $id,
                    'error' => $request->query('error'),
                ]);
            })->name('payment.failure');
            
            // Coupon routes
            Route::post('/apply-coupon', [OrderController::class, 'applyCoupon']);
            Route::post('/remove-coupon', [OrderController::class, 'removeCoupon']);
        });

        // Microservices Integration Example
        Route::prefix('integration')->group(function () {
            Route::prefix('orders')->group(function () {
                Route::post('/', [ApiOrderController::class, 'store']);
                Route::post('/{id}/payment', [ApiOrderController::class, 'processPayment'])->name('api.orders.payment.process');
                Route::get('/{id}/payment/success', function () {
                    return response()->json(['success' => true, 'message' => 'Payment successful']);
                })->name('api.orders.payment.success');
                Route::get('/{id}/payment/failure', function () {
                    return response()->json(['success' => false, 'message' => 'Payment failed']);
                })->name('api.orders.payment.failure');
            });
        });

        // Cart routes
        Route::prefix('cart')->group(function () {
            Route::get('/', [OrderController::class, 'getCart']);
            Route::post('/', [OrderController::class, 'createCart']);
            Route::get('/{cartId}', [OrderController::class, 'showCart']);
            Route::put('/{cartId}', [OrderController::class, 'updateCart']);
            Route::delete('/{cartId}', [OrderController::class, 'clearCart']);
            Route::post('/{cartId}/items', [OrderController::class, 'addCartItem']);
            Route::get('/{cartId}/items/{itemId}', [OrderController::class, 'getCartItem']);
            Route::put('/{cartId}/items/{itemId}', [OrderController::class, 'updateCartItem']);
            Route::delete('/{cartId}/items/{itemId}', [OrderController::class, 'removeCartItem']);
            Route::post('/{cartId}/checkout', [OrderController::class, 'checkout']);
        });

        // Product routes
        Route::prefix('products')->group(function () {
            Route::get('/', [ProductController::class, 'index']);
            Route::get('/paginate', [ProductController::class, 'paginate']); // Add pagination route before {id} route
            Route::post('/', [ProductController::class, 'store']);
            Route::post('/sequence', [ProductController::class, 'updateSequence']); // Add sequence update route
            Route::get('/{id}', [ProductController::class, 'show']);
            Route::put('/{id}', [ProductController::class, 'update']);
            Route::delete('/{id}', [ProductController::class, 'destroy']);
            Route::get('/type/{type}', [ProductController::class, 'getByType']);
            Route::get('/food-type/{foodType}', [ProductController::class, 'getByFoodType']);
            Route::get('/kitchen/{kitchenId}', [ProductController::class, 'getByKitchen']);
            Route::get('/category/{category}', [ProductController::class, 'getByCategory']);
        });

        // Customer routes
        Route::prefix('customers')->group(function () {
            Route::get('/', [CustomerController::class, 'index']);
            Route::post('/', [CustomerController::class, 'store']);
            Route::get('/{id}', [CustomerController::class, 'show']);
            Route::put('/{id}', [CustomerController::class, 'update']);
            Route::delete('/{id}', [CustomerController::class, 'destroy']);
            Route::get('/phone/{phone}', [CustomerController::class, 'getByPhone']);
            Route::get('/email/{email}', [CustomerController::class, 'getByEmail']);
            Route::get('/{id}/addresses', [CustomerController::class, 'getAddresses']);
            Route::get('/{id}/orders', [CustomerController::class, 'getOrders']);
            Route::post('/{id}/otp/send', [CustomerController::class, 'sendOtp']);
            Route::post('/{id}/otp/verify', [CustomerController::class, 'verifyOtp']);
        });

        // Config routes
        Route::prefix('config')->group(function () {
            Route::get('/', [ConfigController::class, 'index']);
            Route::get('/settings', [ConfigController::class, 'settings']);
            Route::get('/{key}', [ConfigController::class, 'show']);
            Route::put('/{key}', [ConfigController::class, 'update']);
        });

        // Timeslot routes
        Route::prefix('timeslots')->group(function () {
            Route::get('/', [TimeslotController::class, 'index']);
            Route::post('/', [TimeslotController::class, 'store']);
            Route::get('/available', [TimeslotController::class, 'available']);
            Route::get('/{id}', [TimeslotController::class, 'show']);
            Route::put('/{id}', [TimeslotController::class, 'update']);
            Route::delete('/{id}', [TimeslotController::class, 'destroy']);
        });

        // Location Mapping routes
        Route::prefix('locations')->group(function () {
            Route::get('/', [LocationMappingController::class, 'index']);
            Route::post('/', [LocationMappingController::class, 'store']);
            Route::get('/by-city', [LocationMappingController::class, 'byCity']);
            Route::get('/by-kitchen', [LocationMappingController::class, 'byKitchen']);
            Route::get('/{id}', [LocationMappingController::class, 'show']);
            Route::put('/{id}', [LocationMappingController::class, 'update']);
            Route::delete('/{id}', [LocationMappingController::class, 'destroy']);
        });

        // Backorder routes
        Route::prefix('backorders')->group(function () {
            Route::get('/', [BackorderController::class, 'index']);
            Route::post('/', [BackorderController::class, 'store']);
            Route::post('/from-order', [BackorderController::class, 'createFromOrder']);
            Route::get('/{id}', [BackorderController::class, 'show']);
            Route::put('/{id}', [BackorderController::class, 'update']);
            Route::delete('/{id}', [BackorderController::class, 'destroy']);
            Route::put('/{id}/complete', [BackorderController::class, 'complete']);
            Route::put('/{id}/cancel', [BackorderController::class, 'cancel']);
        });
    });
});

// API v1 routes (for backward compatibility)
Route::prefix('v1')->group(function () {
    // QuickServe routes
    Route::prefix('quickserve')->group(function () {
        // Health check and metrics
        Route::get('health', [HealthController::class, 'index']);
        Route::get('health/detailed', [HealthController::class, 'detailed'])->middleware('auth:sanctum');
        Route::get('metrics', [MetricsController::class, 'export'])->middleware('auth:sanctum');

        // Order routes
        Route::prefix('orders')->group(function () {
            Route::get('/', [OrderController::class, 'index']);
            Route::post('/', [OrderController::class, 'store']);
            Route::get('/{id}', [OrderController::class, 'show']);
            Route::put('/{id}', [OrderController::class, 'update']);
            Route::delete('/{id}', [OrderController::class, 'destroy']);
            Route::get('/customer/{customerId}', [OrderController::class, 'getByCustomer']);
            Route::patch('/{id}/status', [OrderController::class, 'updateStatus']);
            Route::patch('/{id}/delivery-status', [OrderController::class, 'updateDeliveryStatus']);
            Route::post('/{id}/cancel', [OrderController::class, 'cancel']);
            Route::post('/{id}/payment', [OrderController::class, 'processPayment']);
        });

        // Product routes
        Route::prefix('products')->group(function () {
            Route::get('/', [ProductController::class, 'index']);
            Route::post('/', [ProductController::class, 'store']);
            Route::get('/{id}', [ProductController::class, 'show']);
            Route::put('/{id}', [ProductController::class, 'update']);
            Route::delete('/{id}', [ProductController::class, 'destroy']);
            Route::get('/type/{type}', [ProductController::class, 'getByType']);
            Route::get('/food-type/{foodType}', [ProductController::class, 'getByFoodType']);
            Route::get('/kitchen/{kitchenId}', [ProductController::class, 'getByKitchen']);
            Route::get('/category/{category}', [ProductController::class, 'getByCategory']);
        });

        // Customer routes
        Route::prefix('customers')->group(function () {
            Route::get('/', [CustomerController::class, 'index']);
            Route::post('/', [CustomerController::class, 'store']);
            Route::get('/{id}', [CustomerController::class, 'show']);
            Route::put('/{id}', [CustomerController::class, 'update']);
            Route::delete('/{id}', [CustomerController::class, 'destroy']);
            Route::get('/phone/{phone}', [CustomerController::class, 'getByPhone']);
            Route::get('/email/{email}', [CustomerController::class, 'getByEmail']);
            Route::get('/{id}/addresses', [CustomerController::class, 'getAddresses']);
            Route::get('/{id}/orders', [CustomerController::class, 'getOrders']);
            Route::post('/{id}/otp/send', [CustomerController::class, 'sendOtp']);
            Route::post('/{id}/otp/verify', [CustomerController::class, 'verifyOtp']);
        });

        // Config routes
        Route::prefix('config')->group(function () {
            Route::get('/', [ConfigController::class, 'index']);
            Route::get('/settings', [ConfigController::class, 'settings']);
            Route::get('/{key}', [ConfigController::class, 'show']);
            Route::put('/{key}', [ConfigController::class, 'update']);
        });

        // Timeslot routes
        Route::prefix('timeslots')->group(function () {
            Route::get('/', [TimeslotController::class, 'index']);
            Route::post('/', [TimeslotController::class, 'store']);
            Route::get('/available', [TimeslotController::class, 'available']);
            Route::get('/{id}', [TimeslotController::class, 'show']);
            Route::put('/{id}', [TimeslotController::class, 'update']);
            Route::delete('/{id}', [TimeslotController::class, 'destroy']);
        });

        // Location Mapping routes
        Route::prefix('locations')->group(function () {
            Route::get('/', [LocationMappingController::class, 'index']);
            Route::post('/', [LocationMappingController::class, 'store']);
            Route::get('/by-city', [LocationMappingController::class, 'byCity']);
            Route::get('/by-kitchen', [LocationMappingController::class, 'byKitchen']);
            Route::get('/{id}', [LocationMappingController::class, 'show']);
            Route::put('/{id}', [LocationMappingController::class, 'update']);
            Route::delete('/{id}', [LocationMappingController::class, 'destroy']);
        });

        // Backorder routes
        Route::prefix('backorders')->group(function () {
            Route::get('/', [BackorderController::class, 'index']);
            Route::post('/', [BackorderController::class, 'store']);
            Route::post('/from-order', [BackorderController::class, 'createFromOrder']);
            Route::get('/{id}', [BackorderController::class, 'show']);
            Route::put('/{id}', [BackorderController::class, 'update']);
            Route::delete('/{id}', [BackorderController::class, 'destroy']);
            Route::put('/{id}/complete', [BackorderController::class, 'complete']);
            Route::put('/{id}/cancel', [BackorderController::class, 'cancel']);
        });
    });
});
