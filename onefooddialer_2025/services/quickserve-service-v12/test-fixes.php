<?php
/**
 * Test script to verify the error fixes
 */

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

echo "🧪 Testing Error Fixes\n";
echo "=====================\n\n";

// Test 1: Check meal_calendar query
echo "Test 1: Meal Calendar Query\n";
try {
    $mealItems = DB::table('meal_calendar as mc')
        ->join('products as p', 'mc.product_code', '=', 'p.pk_product_code')
        ->where('mc.fk_product_code', 336)
        ->where('mc.menu', 'meal')
        ->select([
            'p.pk_product_code as product_code',
            'p.name as product_name',
            DB::raw('1 as quantity'),
            'p.unit_price as amount'
        ])
        ->limit(3)
        ->get()
        ->toArray();

    echo "✅ Query executed successfully\n";
    echo "   Found " . count($mealItems) . " meal items\n";
    
    if (count($mealItems) > 0) {
        foreach ($mealItems as $item) {
            echo "   - {$item->product_name}: ₹{$item->amount}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Query failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Tax calculation with proper type
echo "Test 2: Tax Calculation Type Safety\n";
$controller = new \App\Http\Controllers\Api\V2\OrderManagementController(
    new \App\Services\PaymentOrderUpdateService()
);

$reflection = new ReflectionClass($controller);
$method = $reflection->getMethod('calculateOrderTax');
$method->setAccessible(true);

try {
    // Test with float
    $tax1 = $method->invoke($controller, 131.0);
    echo "✅ Float input (131.0): ₹{$tax1}\n";
    
    // Test with string (should be converted)
    $tax2 = $method->invoke($controller, '131.00');
    echo "✅ String input ('131.00'): ₹{$tax2}\n";
    
    if ($tax1 == $tax2) {
        echo "✅ Both inputs produce same result\n";
    } else {
        echo "❌ Different results for same value\n";
    }
} catch (Exception $e) {
    echo "❌ Tax calculation failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Check database connections
echo "Test 3: Database Connection\n";
try {
    $count = DB::table('products')->count();
    echo "✅ Database connected - {$count} products found\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Check recent errors in logs
echo "Test 4: Recent Error Check\n";
$logFile = storage_path('logs/laravel.log');
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $recentErrors = substr_count($logContent, '[' . date('Y-m-d') . ']');
    echo "✅ Log file accessible - {$recentErrors} entries today\n";
    
    // Check for specific errors
    $typeErrors = substr_count($logContent, 'calculateOrderTax(): Argument #1 ($amount) must be of type float');
    $columnErrors = substr_count($logContent, "Unknown column 'p.product_name'");
    
    echo "   Type errors: {$typeErrors}\n";
    echo "   Column errors: {$columnErrors}\n";
} else {
    echo "❌ Log file not found\n";
}

echo "\n🎯 Summary:\n";
echo "- Database queries: Fixed column names\n";
echo "- Type safety: Added float casting\n";
echo "- Error handling: Improved logging\n";
echo "- Services: Running on updated IP addresses\n";

echo "\n✨ Ready for testing with real orders!\n";
