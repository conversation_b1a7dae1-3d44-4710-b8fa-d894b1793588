# Mobile App API Consumption Guide

## 🎯 **Complete Order Journey - APIs You Need to Consume**

### **Step 1: Create Pre-Order (QuickServe Service)**

**Endpoint:** `POST http://*************:8003/api/v2/order-management/create`

**Request Body:**
```json
{
  "customer_id": 3800,
  "customer_name": "Customer User",
  "customer_email": "<EMAIL>",
  "customer_phone": "************",
  "customer_address": "123 Test Street, Test Area, Test City - 400001",
  "location_code": 1001,
  "location_name": "Test Location",
  "city": 1,
  "city_name": "Mumbai",
  "product_code": 342,
  "product_name": "International Breakfast Subscription",
  "product_type": "Meal",
  "quantity": 1,
  "amount": 200.00,
  "days_preference": "1,2,3,4,5",
  "delivery_time": "08:00:00",
  "delivery_end_time": "09:00:00",
  "food_preference": "veg",
  "subscription_days": 15,
  "meal_items": [
    {
      "product_code": 343,
      "product_name": "Breakfast Main Course",
      "quantity": 1,
      "amount": 120.00
    },
    {
      "product_code": 344,
      "product_name": "Breakfast Side Dish",
      "quantity": 1,
      "amount": 50.00
    },
    {
      "product_code": 345,
      "product_name": "Fresh Fruits",
      "quantity": 1,
      "amount": 30.00
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Pre-order created successfully",
  "data": {
    "temp_pre_order_id": 46154,
    "order_no": "ORD202507221240574531",
    "customer_id": 3800,
    "amount": 200,
    "status": "pending",
    "days_preference": "1,2,3,4,5",
    "subscription_days": 15,
    "payment_transaction_id": 30609,
    "payment_service_transaction_id": "TXN30610",
    "payment_urls": {
      "process_payment": "http://*************:8002/api/v2/payments/TXN30610/process",
      "payment_status": "http://*************:8002/api/v2/payments/TXN30610",
      "order_status": "http://*************:8003/api/v2/order-management/pre-order-status/ORD202507221240574531"
    }
  }
}
```

### **Step 2: Process Payment (Payment Service v12 - Your Existing APIs)**

Use your existing 3 Payment Service APIs with the `payment_service_transaction_id` from Step 1:

#### **2.1 Process Payment**
**Endpoint:** `POST http://*************:8002/api/v2/payments/TXN30610/process`

#### **2.2 Payment Callback**
**Endpoint:** `POST http://*************:8002/api/v2/payment/callback`

*Note: These are your existing Payment Service APIs - no changes needed*

### **Step 3: Check Pre-Order Status (QuickServe Service)**

**Endpoint:** `GET http://*************:8003/api/v2/order-management/pre-order-status/ORD202507221240574531`

**Response (Before Payment):**
```json
{
  "success": true,
  "data": {
    "pre_order": {
      "temp_pre_order_id": 46154,
      "order_no": "ORD202507221240574531",
      "customer_id": 3800,
      "customer_name": "Customer User",
      "product_name": "International Breakfast Subscription",
      "amount": "200.00",
      "order_status": "New",
      "days_preference": "1,2,3,4,5",
      "order_days": ["2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-28"],
      "created_date": "2025-07-22 18:10:57"
    },
    "payment_status": {
      "temp_payment_status": "pending",
      "transaction_status": "initiated",
      "gateway": "initiated",
      "gateway_transaction_id": "TXN30610"
    },
    "orders_created": {
      "count": 0,
      "order_details_count": 0,
      "status": "pending"
    }
  }
}
```

**Response (After Payment Success):**
```json
{
  "success": true,
  "data": {
    "pre_order": {
      "temp_pre_order_id": 46154,
      "order_no": "ORD202507221240574531",
      "customer_id": 3800,
      "customer_name": "Customer User",
      "product_name": "International Breakfast Subscription",
      "amount": "200.00",
      "order_status": "New",
      "days_preference": "1,2,3,4,5",
      "order_days": ["2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-28"],
      "created_date": "2025-07-22 18:10:57"
    },
    "payment_status": {
      "temp_payment_status": "success",
      "transaction_status": "completed",
      "gateway": "razorpay",
      "gateway_transaction_id": "TXN30610"
    },
    "orders_created": {
      "count": 12,
      "order_details_count": 36,
      "status": "completed"
    }
  }
}
```

## 📱 **Mobile App Implementation Examples**

### **React Native / JavaScript**
```javascript
class OrderService {
  async createPreOrder(orderData) {
    const response = await fetch('http://*************:8003/api/v2/order-management/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(orderData)
    });
    
    const result = await response.json();
    return result.data;
  }

  async processPayment(transactionId, paymentData) {
    // Use your existing Payment Service API
    const response = await fetch(`http://*************:8002/api/v2/payments/${transactionId}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(paymentData)
    });
    
    return response.json();
  }

  async checkPreOrderStatus(orderNo) {
    const response = await fetch(`http://*************:8003/api/v2/order-management/pre-order-status/${orderNo}`);
    return response.json();
  }

  // Complete order flow
  async completeOrderFlow(orderData, paymentData) {
    try {
      // Step 1: Create pre-order
      const preOrder = await this.createPreOrder(orderData);
      console.log('Pre-order created:', preOrder.order_no);
      
      // Step 2: Process payment
      const payment = await this.processPayment(preOrder.payment_service_transaction_id, paymentData);
      console.log('Payment processed:', payment);
      
      // Step 3: Poll for order creation
      let orderStatus;
      let attempts = 0;
      do {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        orderStatus = await this.checkPreOrderStatus(preOrder.order_no);
        attempts++;
      } while (orderStatus.data.orders_created.status === 'pending' && attempts < 30);
      
      if (orderStatus.data.orders_created.status === 'completed') {
        console.log(`Orders created: ${orderStatus.data.orders_created.count} orders, ${orderStatus.data.orders_created.order_details_count} meal items`);
        return { success: true, orderStatus };
      } else {
        throw new Error('Order creation timeout');
      }
      
    } catch (error) {
      console.error('Order flow failed:', error);
      return { success: false, error: error.message };
    }
  }
}
```

### **Flutter / Dart**
```dart
class OrderService {
  static const String baseUrl = 'http://*************:8003/api/v2';
  static const String paymentUrl = 'http://*************:8002/api/v2';

  Future<Map<String, dynamic>> createPreOrder(Map<String, dynamic> orderData) async {
    final response = await http.post(
      Uri.parse('$baseUrl/order-management/create'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: jsonEncode(orderData),
    );
    
    final result = jsonDecode(response.body);
    return result['data'];
  }

  Future<Map<String, dynamic>> processPayment(String transactionId, Map<String, dynamic> paymentData) async {
    final response = await http.post(
      Uri.parse('$paymentUrl/payments/$transactionId/process'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: jsonEncode(paymentData),
    );
    
    return jsonDecode(response.body);
  }

  Future<Map<String, dynamic>> checkPreOrderStatus(String orderNo) async {
    final response = await http.get(
      Uri.parse('$baseUrl/order-management/pre-order-status/$orderNo'),
    );
    
    return jsonDecode(response.body);
  }

  Future<Map<String, dynamic>> completeOrderFlow(
    Map<String, dynamic> orderData,
    Map<String, dynamic> paymentData,
  ) async {
    try {
      // Step 1: Create pre-order
      final preOrder = await createPreOrder(orderData);
      print('Pre-order created: ${preOrder['order_no']}');
      
      // Step 2: Process payment
      final payment = await processPayment(preOrder['payment_service_transaction_id'], paymentData);
      print('Payment processed: $payment');
      
      // Step 3: Poll for order creation
      Map<String, dynamic> orderStatus;
      int attempts = 0;
      do {
        await Future.delayed(Duration(seconds: 2));
        orderStatus = await checkPreOrderStatus(preOrder['order_no']);
        attempts++;
      } while (orderStatus['data']['orders_created']['status'] == 'pending' && attempts < 30);
      
      if (orderStatus['data']['orders_created']['status'] == 'completed') {
        final ordersCount = orderStatus['data']['orders_created']['count'];
        final detailsCount = orderStatus['data']['orders_created']['order_details_count'];
        print('Orders created: $ordersCount orders, $detailsCount meal items');
        return {'success': true, 'orderStatus': orderStatus};
      } else {
        throw Exception('Order creation timeout');
      }
      
    } catch (error) {
      print('Order flow failed: $error');
      return {'success': false, 'error': error.toString()};
    }
  }
}
```

## 🔄 **Order Flow Summary**

1. **Create Pre-Order** → Returns `payment_service_transaction_id`
2. **Process Payment** → Use your existing Payment Service APIs
3. **Check Status** → Poll until `orders_created.status` = "completed"
4. **Order Complete** → Multiple orders and order details created automatically

## 📊 **What Happens Behind the Scenes**

When payment succeeds:
- ✅ **12 orders created** (one per delivery day)
- ✅ **36 order details created** (3 meal items × 12 orders)
- ✅ **Payment transaction updated** to "completed"
- ✅ **Payment transfer record created** (for Razorpay)
- ✅ **All tables updated** with proper references

Your mobile app only needs to make **3 API calls** for the complete order journey! 🚀
