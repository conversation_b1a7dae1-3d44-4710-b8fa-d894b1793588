<?php
/**
 * Simple test to verify order fixes
 */

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

echo "🧪 Testing Order Fixes\n";
echo "=====================\n\n";

// Test the tax calculation method directly
$controller = new \App\Http\Controllers\Api\V2\OrderManagementController(
    new \App\Services\PaymentOrderUpdateService()
);

// Use reflection to access the protected method
$reflection = new ReflectionClass($controller);
$method = $reflection->getMethod('calculateOrderTax');
$method->setAccessible(true);

// Test tax calculation
$testAmount = 78.00;
$calculatedTax = $method->invoke($controller, $testAmount);

echo "💰 Tax Calculation Test:\n";
echo "   Amount: $testAmount\n";
echo "   Calculated Tax: $calculatedTax\n";
echo "   Expected Tax (5%): " . ($testAmount * 0.05) . "\n";

if (abs($calculatedTax - ($testAmount * 0.05)) < 0.01) {
    echo "✅ Tax calculation working correctly\n\n";
} else {
    echo "❌ Tax calculation incorrect\n\n";
}

// Test meal items retrieval
$tempPreOrder = (object) [
    'product_code' => 339,
    'product_name' => 'Indian Breakfast',
    'order_menu' => 'breakfast',
    'amount' => 78.00
];

$mealItemsMethod = $reflection->getMethod('getMealItemsForOrder');
$mealItemsMethod->setAccessible(true);
$mealItems = $mealItemsMethod->invoke($controller, $tempPreOrder);

echo "🍽️ Meal Items Test:\n";
echo "   Items count: " . count($mealItems) . "\n";
foreach ($mealItems as $item) {
    echo "   - {$item['product_name']}: {$item['amount']}\n";
}

if (count($mealItems) > 0) {
    echo "✅ Meal items retrieval working\n\n";
} else {
    echo "❌ No meal items found\n\n";
}

// Check recent orders for duplicates
echo "📊 Recent Order Analysis:\n";
$recentOrderNos = DB::table('orders')
    ->where('order_date', '>=', '2025-07-23')
    ->select('order_no', DB::raw('COUNT(*) as count'))
    ->groupBy('order_no')
    ->orderBy('count', 'desc')
    ->limit(5)
    ->get();

foreach ($recentOrderNos as $order) {
    $status = $order->count > 1 ? "❌ DUPLICATE" : "✅ SINGLE";
    echo "   {$order->order_no}: {$order->count} orders {$status}\n";
}

echo "\n🎯 Summary:\n";
echo "- Tax calculation: " . (abs($calculatedTax - ($testAmount * 0.05)) < 0.01 ? "✅ Fixed" : "❌ Still broken") . "\n";
echo "- Meal items: " . (count($mealItems) > 0 ? "✅ Working" : "❌ Not working") . "\n";
echo "- Duplicate prevention: Code updated (needs new order to test)\n";
echo "- Due date fix: Code updated (needs new order to test)\n";

echo "\n✨ Ready for testing with new orders!\n";
