<?php

/**
 * Test connectivity from QuickServe to Payment Service
 */

echo "=== TESTING CONNECTIVITY FROM QUICKSERVE ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

// Test different ways to connect to Payment Service
$paymentServiceUrls = [
    'http://*************:8005',
    'http://localhost:8005',
    'http://127.0.0.1:8005'
];

foreach ($paymentServiceUrls as $url) {
    echo "Testing connectivity to: $url\n";
    
    $startTime = microtime(true);
    
    // Test 1: Simple cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$url/api/v2/payments");
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    $connectTime = curl_getinfo($ch, CURLINFO_CONNECT_TIME);
    curl_close($ch);
    
    $totalTime = microtime(true) - $startTime;
    
    if ($curlError) {
        echo "  ❌ cURL Error: $curlError\n";
    } elseif ($httpCode === 200) {
        echo "  ✅ Connected successfully (HTTP $httpCode) in " . round($totalTime, 2) . "s\n";
        echo "  ✅ Connect time: " . round($connectTime, 3) . "s\n";
        break; // Found working URL
    } else {
        echo "  ⚠️  HTTP $httpCode in " . round($totalTime, 2) . "s\n";
    }
    
    echo "\n";
}

// Test 2: Test using Laravel's HTTP client (same as in OrderManagementController)
echo "Testing with Laravel HTTP client...\n";
require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Http;

try {
    $startTime = microtime(true);
    
    $response = Http::timeout(10)
        ->withHeaders([
            'Accept' => 'application/json'
        ])
        ->get('http://*************:8005/api/v2/payments');
        
    $totalTime = microtime(true) - $startTime;
    
    if ($response->successful()) {
        echo "✅ Laravel HTTP client connected successfully in " . round($totalTime, 2) . "s\n";
        echo "✅ Status: " . $response->status() . "\n";
    } else {
        echo "❌ Laravel HTTP client failed with status: " . $response->status() . "\n";
        echo "Response: " . $response->body() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Laravel HTTP client exception: " . $e->getMessage() . "\n";
}

echo "\n=== CONNECTIVITY TEST COMPLETED ===\n";
