<?php

/**
 * Test the exact same payment service call that QuickServe makes
 */

echo "=== TESTING EXACT PAYMENT SERVICE CALL ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

// Bootstrap Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Http;

// Simulate the exact same data that QuickServe sends
$orderNo = 'TEST_EXACT_' . time();
$localTransactionId = 'TXN_LOCAL_' . time();

$paymentData = [
    'company_id' => 8163,
    'unit_id' => 8163,
    'customer_id' => 27802,
    'customer_name' => 'Test Customer',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '9999999999',
    'amount' => 78.00,
    'order_id' => $orderNo,
    'success_url' => "http://192.168.1.161:8003/api/v2/order-management/payment-success/$orderNo",
    'failure_url' => "http://192.168.1.161:8003/api/v2/order-management/payment-failure/$orderNo",
    'context' => 'order_payment',
    'wallet_amount' => 0.00,
    'recurring' => true,
    'discount' => 0.00
];

$paymentServiceUrl = 'http://192.168.1.161:8005';

echo "Testing exact payment service call...\n";
echo "Order No: $orderNo\n";
echo "Local Transaction ID: $localTransactionId\n";
echo "Payment Service URL: $paymentServiceUrl\n\n";

try {
    $startTime = microtime(true);
    
    // Make the exact same call as QuickServe
    $response = Http::timeout(15)
        ->retry(2, 500)
        ->withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'X-Service' => 'quickserve-v12',
            'X-Order-Transaction-ID' => $localTransactionId
        ])
        ->post("{$paymentServiceUrl}/api/v2/payments", $paymentData);
        
    $totalTime = microtime(true) - $startTime;
    
    echo "Payment service call completed in: " . round($totalTime, 2) . " seconds\n";
    echo "Status Code: " . $response->status() . "\n";
    echo "Successful: " . ($response->successful() ? 'Yes' : 'No') . "\n";
    
    if ($response->successful()) {
        $responseData = $response->json();
        echo "✅ Payment initiated successfully\n";
        echo "Transaction ID: " . ($responseData['data']['transaction_id'] ?? 'N/A') . "\n";
        echo "Amount: " . ($responseData['data']['amount'] ?? 'N/A') . "\n";
        echo "Status: " . ($responseData['data']['status'] ?? 'N/A') . "\n";
        
        // Test the payment success callback
        echo "\nTesting payment success callback...\n";
        $paymentSuccessData = [
            'payment_service_transaction_id' => $responseData['data']['transaction_id'] ?? 'TXN_TEST',
            'gateway' => 'razorpay',
            'amount' => 78.00
        ];
        
        $callbackStartTime = microtime(true);
        $callbackResponse = Http::timeout(10)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ])
            ->post("http://192.168.1.161:8003/api/v2/order-management/payment-success/$orderNo", $paymentSuccessData);
            
        $callbackTime = microtime(true) - $callbackStartTime;
        
        echo "Callback completed in: " . round($callbackTime, 2) . " seconds\n";
        echo "Callback Status: " . $callbackResponse->status() . "\n";
        
        if ($callbackResponse->successful()) {
            $callbackData = $callbackResponse->json();
            echo "✅ Payment success callback completed\n";
            echo "Orders Created: " . ($callbackData['data']['created_orders_count'] ?? 'N/A') . "\n";
            echo "Order Details Created: " . ($callbackData['data']['created_order_details_count'] ?? 'N/A') . "\n";
        } else {
            echo "❌ Payment success callback failed\n";
            echo "Response: " . $callbackResponse->body() . "\n";
        }
        
    } else {
        echo "❌ Payment service call failed\n";
        echo "Status: " . $response->status() . "\n";
        echo "Response: " . $response->body() . "\n";
    }
    
} catch (Exception $e) {
    $totalTime = microtime(true) - $startTime;
    echo "❌ Exception occurred after " . round($totalTime, 2) . " seconds\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Exception Type: " . get_class($e) . "\n";
}

$overallTime = microtime(true) - $startTime;
echo "\n=== TEST COMPLETED ===\n";
echo "Total time: " . round($overallTime, 2) . " seconds\n";
echo "Status: " . ($overallTime < 30 ? "✅ WITHIN TIME LIMIT" : "❌ EXCEEDED TIME LIMIT") . "\n";

if ($overallTime >= 30) {
    echo "WARNING: This explains why the order creation is timing out!\n";
} else {
    echo "✅ The exact same call works fine - issue might be elsewhere\n";
}
