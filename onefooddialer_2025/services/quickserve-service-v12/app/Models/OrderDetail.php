<?php

declare(strict_types=1);
// @tag QS_LARAVEL - QuickServe Laravel 12 Migration

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderDetail extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_details';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_order_detail_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'ref_order_no',
        'meal_code',
        'product_code',
        'product_name',
        'quantity',
        'product_type',
        'order_date',
        'product_amount',
        'product_tax',
        'product_subtype',
        'product_generic_code',
        'product_generic_name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'float',
        'tax' => 'float',
        'quantity' => 'integer',
        'company_id' => 'integer',
        'unit_id' => 'integer',
    ];

    /**
     * Get the order that owns the order detail.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'ref_order_no', 'order_no');
    }

    /**
     * Get the product that owns the order detail.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_code', 'product_code');
    }

    /**
     * Get the total amount including tax.
     */
    public function getTotalAmount(): float
    {
        return $this->amount + $this->tax;
    }

    /**
     * Get the unit price.
     */
    public function getUnitPrice(): float
    {
        return $this->quantity > 0 ? $this->amount / $this->quantity : 0;
    }

    /**
     * Get the unit tax.
     */
    public function getUnitTax(): float
    {
        return $this->quantity > 0 ? $this->tax / $this->quantity : 0;
    }
}
