<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Order;
use App\Models\OrderDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Service to handle payment-to-order updates
 * This service processes payment confirmations and updates order status accordingly
 */
class PaymentOrderUpdateService
{
    /**
     * Process payment confirmation and update order status
     *
     * @param array $paymentData Payment confirmation data
     * @return array Result of the update operation
     * @throws Exception
     */
    public function processPaymentConfirmation(array $paymentData): array
    {
        DB::beginTransaction();
        
        try {
            // Validate required payment data
            $this->validatePaymentData($paymentData);
            
            // Extract order information
            $orderNo = $paymentData['order_no'] ?? $paymentData['pre_order_id'];
            $transactionId = $paymentData['transaction_id'] ?? $paymentData['gateway_transaction_id'];
            $paymentStatus = $paymentData['status'];
            $paymentAmount = $paymentData['amount'] ?? $paymentData['payment_amount'];
            $paymentMethod = $paymentData['gateway'] ?? $paymentData['payment_method'];
            
            // Find the order
            $order = Order::where('order_no', $orderNo)->first();
            
            if (!$order) {
                throw new Exception("Order not found: {$orderNo}");
            }
            
            // Process based on payment status
            if ($paymentStatus === 'completed' || $paymentStatus === 'success') {
                $result = $this->processSuccessfulPayment($order, $paymentData);
            } elseif ($paymentStatus === 'failed' || $paymentStatus === 'failure') {
                $result = $this->processFailedPayment($order, $paymentData);
            } else {
                $result = $this->processPendingPayment($order, $paymentData);
            }
            
            DB::commit();
            
            Log::info('Payment confirmation processed successfully', [
                'order_no' => $orderNo,
                'transaction_id' => $transactionId,
                'status' => $paymentStatus,
                'amount' => $paymentAmount
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to process payment confirmation', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData,
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Process successful payment
     *
     * @param Order $order
     * @param array $paymentData
     * @return array
     */
    protected function processSuccessfulPayment(Order $order, array $paymentData): array
    {
        // Update order status and payment information
        $order->update([
            'amount_paid' => 1, // Boolean flag indicating payment received
            'payment_mode' => $paymentData['gateway'] ?? $paymentData['payment_method'],
            'order_status' => 'Confirmed', // Update status from 'New' to 'Confirmed'
            'last_modified' => now()
        ]);
        
        // Update payment transaction status
        $this->updatePaymentTransaction($order->order_no, 'completed', $paymentData);
        
        // If it's a recurring order, create future orders based on days_preference
        if ($order->recurring_status === '1' && $order->days_preference) {
            $this->createRecurringOrders($order);
        }
        
        // Log successful payment processing
        Log::info('Order payment processed successfully', [
            'order_id' => $order->pk_order_no,
            'order_no' => $order->order_no,
            'customer_id' => $order->customer_code,
            'amount' => $paymentData['amount'] ?? $paymentData['payment_amount'],
            'payment_method' => $paymentData['gateway'] ?? $paymentData['payment_method']
        ]);
        
        return [
            'success' => true,
            'message' => 'Payment processed successfully',
            'order_id' => $order->pk_order_no,
            'order_no' => $order->order_no,
            'status' => 'Confirmed',
            'amount_paid' => $paymentData['amount'] ?? $paymentData['payment_amount']
        ];
    }
    
    /**
     * Process failed payment
     *
     * @param Order $order
     * @param array $paymentData
     * @return array
     */
    protected function processFailedPayment(Order $order, array $paymentData): array
    {
        // Update order status for failed payment
        $order->update([
            'order_status' => 'Payment Failed',
            'last_modified' => now()
        ]);
        
        // Update payment transaction status
        $this->updatePaymentTransaction($order->order_no, 'failed', $paymentData);
        
        Log::warning('Order payment failed', [
            'order_id' => $order->pk_order_no,
            'order_no' => $order->order_no,
            'customer_id' => $order->customer_code,
            'reason' => $paymentData['failure_reason'] ?? 'Payment gateway failure'
        ]);
        
        return [
            'success' => false,
            'message' => 'Payment failed',
            'order_id' => $order->pk_order_no,
            'order_no' => $order->order_no,
            'status' => 'Payment Failed',
            'reason' => $paymentData['failure_reason'] ?? 'Payment gateway failure'
        ];
    }
    
    /**
     * Process pending payment
     *
     * @param Order $order
     * @param array $paymentData
     * @return array
     */
    protected function processPendingPayment(Order $order, array $paymentData): array
    {
        // Update payment transaction status but keep order as is
        $this->updatePaymentTransaction($order->order_no, 'pending', $paymentData);
        
        return [
            'success' => true,
            'message' => 'Payment is pending',
            'order_id' => $order->pk_order_no,
            'order_no' => $order->order_no,
            'status' => $order->order_status,
            'payment_status' => 'pending'
        ];
    }
    
    /**
     * Update payment transaction record
     *
     * @param string $orderNo
     * @param string $status
     * @param array $paymentData
     * @return void
     */
    protected function updatePaymentTransaction(string $orderNo, string $status, array $paymentData): void
    {
        DB::table('payment_transaction')
            ->where('pre_order_id', $orderNo)
            ->update([
                'status' => $status,
                'gateway_transaction_id' => $paymentData['transaction_id'] ?? $paymentData['gateway_transaction_id'],
                'modified_date' => now()
            ]);
    }
    
    /**
     * Create recurring orders based on days preference
     *
     * @param Order $order
     * @return void
     */
    protected function createRecurringOrders(Order $order): void
    {
        if (!$order->days_preference) {
            return;
        }
        
        // Parse days preference (e.g., "1,2,3,4,5" for Monday to Friday)
        $selectedDays = explode(',', $order->days_preference);
        
        // Create orders for the next 30 days based on selected days
        $startDate = now()->addDay();
        $endDate = now()->addDays(30);
        
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dayOfWeek = $date->dayOfWeek; // 0=Sunday, 1=Monday, etc.
            
            if (in_array((string)$dayOfWeek, $selectedDays)) {
                $this->createRecurringOrder($order, $date);
            }
        }
    }
    
    /**
     * Create a single recurring order
     *
     * @param Order $originalOrder
     * @param \Carbon\Carbon $date
     * @return void
     */
    protected function createRecurringOrder(Order $originalOrder, $date): void
    {
        // Generate new order number
        $newOrderNo = 'ORD' . $date->format('YmdHis') . rand(1000, 9999);
        
        // Create new order record
        $newOrder = $originalOrder->replicate();
        $newOrder->order_no = $newOrderNo;
        $newOrder->ref_order = $originalOrder->pk_order_no;
        $newOrder->order_date = $date->format('Y-m-d');
        $newOrder->order_status = 'Scheduled';
        $newOrder->amount_paid = 0; // Will be paid when delivered
        $newOrder->created_date = now();
        $newOrder->save();
        
        // Copy order details
        $originalDetails = OrderDetail::where('ref_order_no', $originalOrder->order_no)->get();
        
        foreach ($originalDetails as $detail) {
            $newDetail = $detail->replicate();
            $newDetail->ref_order_no = $newOrderNo;
            $newDetail->order_date = $date->format('Y-m-d');
            $newDetail->save();
        }
        
        Log::info('Recurring order created', [
            'original_order' => $originalOrder->order_no,
            'new_order' => $newOrderNo,
            'delivery_date' => $date->format('Y-m-d')
        ]);
    }
    
    /**
     * Validate payment data
     *
     * @param array $paymentData
     * @return void
     * @throws Exception
     */
    protected function validatePaymentData(array $paymentData): void
    {
        $requiredFields = ['status'];
        $orderFields = ['order_no', 'pre_order_id'];
        
        // Check required fields
        foreach ($requiredFields as $field) {
            if (!isset($paymentData[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }
        
        // Check that at least one order identifier is present
        $hasOrderId = false;
        foreach ($orderFields as $field) {
            if (isset($paymentData[$field])) {
                $hasOrderId = true;
                break;
            }
        }
        
        if (!$hasOrderId) {
            throw new Exception("Missing order identifier. Required: " . implode(' or ', $orderFields));
        }
    }
}
