<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V2;

use App\Models\Order;
use App\Models\OrderDetail;
use App\Services\PaymentOrderUpdateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

/**
 * Controller for comprehensive order management
 */
class OrderManagementController extends BaseController
{
    public function __construct(
        protected PaymentOrderUpdateService $paymentOrderUpdateService
    ) {}

    /**
     * Create a new order with order details
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createOrder(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validated = $request->validate([
                'customer_id' => 'required|integer',
                'customer_name' => 'required|string|max:45',
                'customer_email' => 'required|email|max:45',
                'customer_phone' => 'required|string|max:15',
                'customer_address' => 'required|string|max:250',
                'location_code' => 'required|integer',
                'location_name' => 'required|string|max:45',
                'city' => 'required|integer',
                'city_name' => 'required|string|max:45',
                'product_code' => 'required|integer',
                'product_name' => 'required|string|max:255',
                'product_type' => 'required|string|max:60',
                'quantity' => 'required|integer|min:1',
                'amount' => 'required|numeric|min:0',
                'days_preference' => 'required|string', // e.g., "1,2,3,4,5"
                'delivery_time' => 'nullable|string',
                'delivery_end_time' => 'nullable|string',
                'food_preference' => 'nullable|string|max:255',
                'meal_items' => 'required|array',
                'meal_items.*.product_code' => 'required|integer',
                'meal_items.*.product_name' => 'required|string|max:255',
                'meal_items.*.quantity' => 'required|integer|min:1',
                'meal_items.*.amount' => 'required|numeric|min:0',
            ]);

            DB::beginTransaction();

            // Generate unique order number
            $orderNo = $this->generateOrderNumber();

            // Create main order record
            $order = Order::create([
                'company_id' => 8163,
                'unit_id' => 8163,
                'fk_kitchen_code' => 1,
                'ref_order' => 0,
                'order_no' => $orderNo,
                'auth_id' => $validated['customer_id'],
                'customer_code' => $validated['customer_id'],
                'customer_name' => $validated['customer_name'],
                'food_preference' => $validated['food_preference'] ?? 'veg',
                'phone' => $validated['customer_phone'],
                'email_address' => $validated['customer_email'],
                'location_code' => $validated['location_code'],
                'location_name' => $validated['location_name'],
                'city' => $validated['city'],
                'city_name' => $validated['city_name'],
                'product_code' => $validated['product_code'],
                'product_name' => $validated['product_name'],
                'product_description' => $validated['product_name'],
                'product_type' => $validated['product_type'],
                'quantity' => $validated['quantity'],
                'product_price' => $validated['amount'],
                'amount' => $validated['amount'],
                'applied_discount' => 0.00,
                'amount_paid' => 0,
                'tax' => 0.00,
                'delivery_charges' => 0.00,
                'service_charges' => 0.00,
                'order_status' => 'New',
                'order_date' => now()->format('Y-m-d'),
                'due_date' => now()->format('Y-m-d'),
                'ship_address' => $validated['customer_address'],
                'delivery_status' => 'Pending',
                'invoice_status' => 'Unbill',
                'order_menu' => strtolower($validated['product_type']),
                'inventory_type' => 'perishable',
                'food_type' => $validated['food_preference'] ?? 'veg',
                'payment_mode' => null,
                'days_preference' => $validated['days_preference'],
                'delivery_type' => 'delivery',
                'delivery_time' => $validated['delivery_time'] ?? '09:00:00',
                'delivery_end_time' => $validated['delivery_end_time'] ?? '10:00:00',
                'recurring_status' => '1',
                'created_date' => now(),
            ]);

            // Create order details for each meal item
            foreach ($validated['meal_items'] as $item) {
                OrderDetail::create([
                    'company_id' => 8163,
                    'unit_id' => 8163,
                    'ref_order_no' => $orderNo,
                    'meal_code' => $validated['product_code'],
                    'product_code' => $item['product_code'],
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'product_type' => 'Meal',
                    'order_date' => now()->format('Y-m-d'),
                    'product_amount' => $item['amount'],
                    'product_tax' => 0.00,
                    'product_subtype' => 'specific',
                    'product_generic_code' => $item['product_code'],
                    'product_generic_name' => $item['product_name'],
                ]);
            }

            // Create payment transaction and initiate payment with payment service
            $paymentResult = $this->createPaymentTransaction($order, $validated);

            DB::commit();

            Log::info('Order created successfully', [
                'order_no' => $orderNo,
                'customer_id' => $validated['customer_id'],
                'amount' => $validated['amount'],
                'local_transaction_id' => $paymentResult['local_transaction_id'],
                'payment_service_transaction_id' => $paymentResult['payment_service_response']['data']['transaction_id'] ?? null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => [
                    'order_id' => $order->pk_order_no,
                    'order_no' => $orderNo,
                    'customer_id' => $validated['customer_id'],
                    'amount' => $validated['amount'],
                    'status' => 'New',
                    'days_preference' => $validated['days_preference'],
                    'local_transaction_id' => $paymentResult['local_transaction_id'],
                    'payment_service_transaction_id' => $paymentResult['payment_service_response']['data']['transaction_id'] ?? null,
                    'payment_urls' => [
                        'process_payment' => config('services.payment.url') . "/api/v2/payments/{$paymentResult['payment_service_response']['data']['transaction_id']}/process",
                        'payment_status' => config('services.payment.url') . "/api/v2/payments/{$paymentResult['payment_service_response']['data']['transaction_id']}",
                        'order_status' => config('app.url') . "/api/v2/order-management/details/{$orderNo}"
                    ]
                ]
            ], 201);

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('Order creation failed', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Order creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order details with payment status
     *
     * @param string $orderNo
     * @return JsonResponse
     */
    public function getOrderDetails(string $orderNo): JsonResponse
    {
        try {
            // Get order
            $order = Order::where('order_no', $orderNo)->first();
            
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            // Get order details
            $orderDetails = OrderDetail::where('ref_order_no', $orderNo)->get();

            // Get payment transaction
            $paymentTransaction = DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'order' => [
                        'order_id' => $order->pk_order_no,
                        'order_no' => $order->order_no,
                        'customer_id' => $order->customer_code,
                        'customer_name' => $order->customer_name,
                        'customer_email' => $order->email_address,
                        'customer_phone' => $order->phone,
                        'customer_address' => $order->ship_address,
                        'product_name' => $order->product_name,
                        'product_type' => $order->product_type,
                        'quantity' => $order->quantity,
                        'amount' => $order->amount,
                        'order_status' => $order->order_status,
                        'payment_mode' => $order->payment_mode,
                        'amount_paid' => $order->amount_paid,
                        'days_preference' => $order->days_preference,
                        'delivery_status' => $order->delivery_status,
                        'order_date' => $order->order_date,
                        'delivery_time' => $order->delivery_time,
                        'delivery_end_time' => $order->delivery_end_time,
                        'recurring_status' => $order->recurring_status,
                    ],
                    'meal_items' => $orderDetails->map(function ($detail) {
                        return [
                            'product_code' => $detail->product_code,
                            'product_name' => $detail->product_name,
                            'quantity' => $detail->quantity,
                            'amount' => $detail->product_amount,
                        ];
                    }),
                    'payment_transaction' => $paymentTransaction ? [
                        'transaction_id' => $paymentTransaction->pk_transaction_id,
                        'gateway_transaction_id' => $paymentTransaction->gateway_transaction_id,
                        'status' => $paymentTransaction->status,
                        'gateway' => $paymentTransaction->gateway,
                        'payment_amount' => $paymentTransaction->payment_amount,
                        'created_date' => $paymentTransaction->created_date,
                    ] : null
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get order details', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get order details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get orders by customer with pagination
     *
     * @param Request $request
     * @param int $customerId
     * @return JsonResponse
     */
    public function getCustomerOrders(Request $request, int $customerId): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 10);
            $status = $request->input('status');

            $query = Order::where('customer_code', $customerId);

            if ($status) {
                $query->where('order_status', $status);
            }

            $orders = $query->orderBy('pk_order_no', 'desc')
                           ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $orders->items(),
                'pagination' => [
                    'current_page' => $orders->currentPage(),
                    'per_page' => $orders->perPage(),
                    'total' => $orders->total(),
                    'last_page' => $orders->lastPage(),
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get customer orders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate unique order number
     *
     * @return string
     */
    protected function generateOrderNumber(): string
    {
        return 'ORD' . now()->format('YmdHis') . rand(1000, 9999);
    }

    /**
     * Create payment transaction record and initiate payment with payment service
     *
     * @param Order $order
     * @param array $validated
     * @return array
     */
    protected function createPaymentTransaction(Order $order, array $validated): array
    {
        // Create local payment transaction record for tracking
        $transactionId = DB::table('payment_transaction')->insertGetId([
            'company_id' => 8163,
            'unit_id' => 8163,
            'customer_id' => $validated['customer_id'],
            'customer_email' => $validated['customer_email'],
            'customer_phone' => $validated['customer_phone'],
            'customer_name' => $validated['customer_name'],
            'payment_amount' => $validated['amount'],
            'transaction_charges' => round($validated['amount'] * 0.03, 2), // 3% transaction fee
            'wallet_amount' => 0.00,
            'pre_order_id' => $order->order_no,
            'gateway' => 'pending',
            'status' => 'pending',
            'gateway_transaction_id' => null,
            'description' => "Payment for {$validated['product_name']} subscription",
            'created_date' => now(),
            'transaction_by' => 'api',
            'referer' => 'order_api',
            'success_url' => config('app.url') . "/api/v2/order-management/payment-success/{$order->order_no}",
            'failure_url' => config('app.url') . "/api/v2/order-management/payment-failure/{$order->order_no}",
            'context' => 'order_payment',
            'discount' => 0.00,
            'recurring' => 1,
        ]);

        // Initiate payment with payment service
        $paymentServiceResponse = $this->initiatePaymentWithPaymentService($order, $validated, $transactionId);

        return [
            'local_transaction_id' => $transactionId,
            'payment_service_response' => $paymentServiceResponse
        ];
    }

    /**
     * Initiate payment with payment service v12
     *
     * @param Order $order
     * @param array $validated
     * @param int $localTransactionId
     * @return array
     */
    protected function initiatePaymentWithPaymentService(Order $order, array $validated, int $localTransactionId): array
    {
        try {
            $paymentServiceUrl = config('services.payment.url', 'http://localhost:8002');

            $paymentData = [
                'customer_id' => $validated['customer_id'],
                'customer_email' => $validated['customer_email'],
                'customer_phone' => $validated['customer_phone'],
                'customer_name' => $validated['customer_name'],
                'amount' => $validated['amount'],
                'transaction_charges' => round($validated['amount'] * 0.03, 2),
                'wallet_amount' => 0.00,
                'order_id' => $order->order_no, // Use order_no as order_id for payment service
                'referer' => 'quickserve_order_api',
                'success_url' => config('app.url') . "/api/v2/order-management/payment-success/{$order->order_no}",
                'failure_url' => config('app.url') . "/api/v2/order-management/payment-failure/{$order->order_no}",
                'context' => 'order_payment',
                'recurring' => true,
                'discount' => 0.00
            ];

            // Call payment service to initiate payment
            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Service' => 'quickserve-v12',
                    'X-Order-Transaction-ID' => $localTransactionId
                ])
                ->post("{$paymentServiceUrl}/api/v2/payments", $paymentData);

            if ($response->successful()) {
                $responseData = $response->json();

                // Update local transaction with payment service transaction ID
                DB::table('payment_transaction')
                    ->where('pk_transaction_id', $localTransactionId)
                    ->update([
                        'gateway_transaction_id' => $responseData['data']['transaction_id'] ?? null,
                        'modified_date' => now()
                    ]);

                Log::info('Payment initiated with payment service', [
                    'order_no' => $order->order_no,
                    'local_transaction_id' => $localTransactionId,
                    'payment_service_transaction_id' => $responseData['data']['transaction_id'] ?? null
                ]);

                return $responseData;
            } else {
                throw new Exception('Payment service initiation failed: ' . $response->body());
            }

        } catch (Exception $e) {
            Log::error('Failed to initiate payment with payment service', [
                'order_no' => $order->order_no,
                'local_transaction_id' => $localTransactionId,
                'error' => $e->getMessage()
            ]);

            // Update local transaction status to failed
            DB::table('payment_transaction')
                ->where('pk_transaction_id', $localTransactionId)
                ->update([
                    'status' => 'failed',
                    'description' => 'Payment service initiation failed: ' . $e->getMessage(),
                    'modified_date' => now()
                ]);

            throw $e;
        }
    }

    /**
     * Handle payment success callback from payment service
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function handlePaymentSuccess(Request $request, string $orderNo): JsonResponse
    {
        try {
            // Find the order
            $order = Order::where('order_no', $orderNo)->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            // Update order status to confirmed
            $order->update([
                'order_status' => 'Confirmed',
                'amount_paid' => 1,
                'payment_mode' => $request->input('gateway', 'online'),
                'last_modified' => now()
            ]);

            // Update local payment transaction
            DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->update([
                    'status' => 'completed',
                    'gateway' => $request->input('gateway', 'online'),
                    'gateway_transaction_id' => $request->input('payment_service_transaction_id'),
                    'modified_date' => now()
                ]);

            // Create recurring orders if applicable
            if ($order->recurring_status === '1' && $order->days_preference) {
                $this->paymentOrderUpdateService->createRecurringOrders($order);
            }

            Log::info('Payment success processed for order', [
                'order_no' => $orderNo,
                'order_id' => $order->pk_order_no,
                'payment_service_transaction_id' => $request->input('payment_service_transaction_id')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment success processed',
                'data' => [
                    'order_no' => $orderNo,
                    'order_status' => 'Confirmed',
                    'payment_status' => 'completed'
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to process payment success', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment success: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment failure callback from payment service
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function handlePaymentFailure(Request $request, string $orderNo): JsonResponse
    {
        try {
            // Find the order
            $order = Order::where('order_no', $orderNo)->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            // Update order status to payment failed
            $order->update([
                'order_status' => 'Payment Failed',
                'last_modified' => now()
            ]);

            // Update local payment transaction
            DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->update([
                    'status' => 'failed',
                    'gateway' => $request->input('gateway', 'online'),
                    'gateway_transaction_id' => $request->input('payment_service_transaction_id'),
                    'modified_date' => now()
                ]);

            Log::warning('Payment failure processed for order', [
                'order_no' => $orderNo,
                'order_id' => $order->pk_order_no,
                'failure_reason' => $request->input('failure_reason', 'Payment gateway failure')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment failure processed',
                'data' => [
                    'order_no' => $orderNo,
                    'order_status' => 'Payment Failed',
                    'payment_status' => 'failed',
                    'failure_reason' => $request->input('failure_reason', 'Payment gateway failure')
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to process payment failure', [
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment failure: ' . $e->getMessage()
            ], 500);
        }
    }
}
