<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V2;

use App\Models\Order;
use App\Models\OrderDetail;
use App\Services\PaymentOrderUpdateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Controller for comprehensive order management
 */
class OrderManagementController extends BaseController
{
    public function __construct(
        protected PaymentOrderUpdateService $paymentOrderUpdateService
    ) {}

    /**
     * Create a new order with order details
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createOrder(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validated = $request->validate([
                'customer_id' => 'required|integer',
                'customer_name' => 'required|string|max:45',
                'customer_email' => 'required|email|max:45',
                'customer_phone' => 'required|string|max:15',
                'customer_address' => 'required|string|max:250',
                'location_code' => 'required|integer',
                'location_name' => 'required|string|max:45',
                'city' => 'required|integer',
                'city_name' => 'required|string|max:45',
                'product_code' => 'required|integer',
                'product_name' => 'required|string|max:255',
                'product_type' => 'required|string|max:60',
                'quantity' => 'required|integer|min:1',
                'amount' => 'required|numeric|min:0',
                'days_preference' => 'required|string', // e.g., "1,2,3,4,5"
                'delivery_time' => 'nullable|string',
                'delivery_end_time' => 'nullable|string',
                'food_preference' => 'nullable|string|max:255',
                'meal_items' => 'required|array',
                'meal_items.*.product_code' => 'required|integer',
                'meal_items.*.product_name' => 'required|string|max:255',
                'meal_items.*.quantity' => 'required|integer|min:1',
                'meal_items.*.amount' => 'required|numeric|min:0',
            ]);

            DB::beginTransaction();

            // Generate unique order number
            $orderNo = $this->generateOrderNumber();

            // Create main order record
            $order = Order::create([
                'company_id' => 8163,
                'unit_id' => 8163,
                'fk_kitchen_code' => 1,
                'ref_order' => 0,
                'order_no' => $orderNo,
                'auth_id' => $validated['customer_id'],
                'customer_code' => $validated['customer_id'],
                'customer_name' => $validated['customer_name'],
                'food_preference' => $validated['food_preference'] ?? 'veg',
                'phone' => $validated['customer_phone'],
                'email_address' => $validated['customer_email'],
                'location_code' => $validated['location_code'],
                'location_name' => $validated['location_name'],
                'city' => $validated['city'],
                'city_name' => $validated['city_name'],
                'product_code' => $validated['product_code'],
                'product_name' => $validated['product_name'],
                'product_description' => $validated['product_name'],
                'product_type' => $validated['product_type'],
                'quantity' => $validated['quantity'],
                'product_price' => $validated['amount'],
                'amount' => $validated['amount'],
                'applied_discount' => 0.00,
                'amount_paid' => 0,
                'tax' => 0.00,
                'delivery_charges' => 0.00,
                'service_charges' => 0.00,
                'order_status' => 'New',
                'order_date' => now()->format('Y-m-d'),
                'due_date' => now()->format('Y-m-d'),
                'ship_address' => $validated['customer_address'],
                'delivery_status' => 'Pending',
                'invoice_status' => 'Unbill',
                'order_menu' => strtolower($validated['product_type']),
                'inventory_type' => 'perishable',
                'food_type' => $validated['food_preference'] ?? 'veg',
                'payment_mode' => null,
                'days_preference' => $validated['days_preference'],
                'delivery_type' => 'delivery',
                'delivery_time' => $validated['delivery_time'] ?? '09:00:00',
                'delivery_end_time' => $validated['delivery_end_time'] ?? '10:00:00',
                'recurring_status' => '1',
                'created_date' => now(),
            ]);

            // Create order details for each meal item
            foreach ($validated['meal_items'] as $item) {
                OrderDetail::create([
                    'company_id' => 8163,
                    'unit_id' => 8163,
                    'ref_order_no' => $orderNo,
                    'meal_code' => $validated['product_code'],
                    'product_code' => $item['product_code'],
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'product_type' => 'Meal',
                    'order_date' => now()->format('Y-m-d'),
                    'product_amount' => $item['amount'],
                    'product_tax' => 0.00,
                    'product_subtype' => 'specific',
                    'product_generic_code' => $item['product_code'],
                    'product_generic_name' => $item['product_name'],
                ]);
            }

            // Create payment transaction record
            $transactionId = $this->createPaymentTransaction($order, $validated);

            DB::commit();

            Log::info('Order created successfully', [
                'order_no' => $orderNo,
                'customer_id' => $validated['customer_id'],
                'amount' => $validated['amount'],
                'transaction_id' => $transactionId
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => [
                    'order_id' => $order->pk_order_no,
                    'order_no' => $orderNo,
                    'customer_id' => $validated['customer_id'],
                    'amount' => $validated['amount'],
                    'status' => 'New',
                    'days_preference' => $validated['days_preference'],
                    'transaction_id' => $transactionId,
                    'payment_url' => "http://localhost:8003/api/v2/payment/status/{$orderNo}"
                ]
            ], 201);

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('Order creation failed', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Order creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order details with payment status
     *
     * @param string $orderNo
     * @return JsonResponse
     */
    public function getOrderDetails(string $orderNo): JsonResponse
    {
        try {
            // Get order
            $order = Order::where('order_no', $orderNo)->first();
            
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            // Get order details
            $orderDetails = OrderDetail::where('ref_order_no', $orderNo)->get();

            // Get payment transaction
            $paymentTransaction = DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'order' => [
                        'order_id' => $order->pk_order_no,
                        'order_no' => $order->order_no,
                        'customer_id' => $order->customer_code,
                        'customer_name' => $order->customer_name,
                        'customer_email' => $order->email_address,
                        'customer_phone' => $order->phone,
                        'customer_address' => $order->ship_address,
                        'product_name' => $order->product_name,
                        'product_type' => $order->product_type,
                        'quantity' => $order->quantity,
                        'amount' => $order->amount,
                        'order_status' => $order->order_status,
                        'payment_mode' => $order->payment_mode,
                        'amount_paid' => $order->amount_paid,
                        'days_preference' => $order->days_preference,
                        'delivery_status' => $order->delivery_status,
                        'order_date' => $order->order_date,
                        'delivery_time' => $order->delivery_time,
                        'delivery_end_time' => $order->delivery_end_time,
                        'recurring_status' => $order->recurring_status,
                    ],
                    'meal_items' => $orderDetails->map(function ($detail) {
                        return [
                            'product_code' => $detail->product_code,
                            'product_name' => $detail->product_name,
                            'quantity' => $detail->quantity,
                            'amount' => $detail->product_amount,
                        ];
                    }),
                    'payment_transaction' => $paymentTransaction ? [
                        'transaction_id' => $paymentTransaction->pk_transaction_id,
                        'gateway_transaction_id' => $paymentTransaction->gateway_transaction_id,
                        'status' => $paymentTransaction->status,
                        'gateway' => $paymentTransaction->gateway,
                        'payment_amount' => $paymentTransaction->payment_amount,
                        'created_date' => $paymentTransaction->created_date,
                    ] : null
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get order details', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get order details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get orders by customer with pagination
     *
     * @param Request $request
     * @param int $customerId
     * @return JsonResponse
     */
    public function getCustomerOrders(Request $request, int $customerId): JsonResponse
    {
        try {
            $perPage = $request->input('per_page', 10);
            $status = $request->input('status');

            $query = Order::where('customer_code', $customerId);

            if ($status) {
                $query->where('order_status', $status);
            }

            $orders = $query->orderBy('pk_order_no', 'desc')
                           ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $orders->items(),
                'pagination' => [
                    'current_page' => $orders->currentPage(),
                    'per_page' => $orders->perPage(),
                    'total' => $orders->total(),
                    'last_page' => $orders->lastPage(),
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get customer orders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate unique order number
     *
     * @return string
     */
    protected function generateOrderNumber(): string
    {
        return 'ORD' . now()->format('YmdHis') . rand(1000, 9999);
    }

    /**
     * Create payment transaction record
     *
     * @param Order $order
     * @param array $validated
     * @return int
     */
    protected function createPaymentTransaction(Order $order, array $validated): int
    {
        $transactionId = DB::table('payment_transaction')->insertGetId([
            'company_id' => 8163,
            'unit_id' => 8163,
            'customer_id' => $validated['customer_id'],
            'customer_email' => $validated['customer_email'],
            'customer_phone' => $validated['customer_phone'],
            'customer_name' => $validated['customer_name'],
            'payment_amount' => $validated['amount'],
            'transaction_charges' => round($validated['amount'] * 0.03, 2), // 3% transaction fee
            'wallet_amount' => 0.00,
            'pre_order_id' => $order->order_no,
            'gateway' => 'pending',
            'status' => 'pending',
            'gateway_transaction_id' => null,
            'description' => "Payment for {$validated['product_name']} subscription",
            'created_date' => now(),
            'transaction_by' => 'api',
            'referer' => 'order_api',
            'success_url' => "http://localhost:8003/api/v2/payment/success/{$order->order_no}",
            'failure_url' => "http://localhost:8003/api/v2/payment/failure/{$order->order_no}",
            'context' => 'order_payment',
            'discount' => 0.00,
            'recurring' => 1,
        ]);

        return $transactionId;
    }
}
