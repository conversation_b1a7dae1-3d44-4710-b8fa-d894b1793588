<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V2;

use App\Services\PaymentOrderUpdateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Controller to handle payment webhooks and callbacks
 */
class PaymentWebhookController extends BaseController
{
    public function __construct(
        protected PaymentOrderUpdateService $paymentOrderUpdateService
    ) {}

    /**
     * Handle payment webhook from payment gateways
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        try {
            // Log incoming webhook
            Log::info('Payment webhook received', [
                'headers' => $request->headers->all(),
                'payload' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Verify webhook signature (implement based on your payment gateway)
            if (!$this->verifyWebhookSignature($request)) {
                Log::warning('Invalid webhook signature', [
                    'payload' => $request->all(),
                    'ip' => $request->ip()
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid signature'
                ], 401);
            }

            // Process payment confirmation
            $result = $this->paymentOrderUpdateService->processPaymentConfirmation($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully',
                'data' => $result
            ]);

        } catch (Exception $e) {
            Log::error('Webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment callback (redirect from payment gateway)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handleCallback(Request $request): JsonResponse
    {
        try {
            // Log callback
            Log::info('Payment callback received', [
                'payload' => $request->all(),
                'ip' => $request->ip()
            ]);

            // Process payment confirmation
            $result = $this->paymentOrderUpdateService->processPaymentConfirmation($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Payment callback processed successfully',
                'data' => $result
            ]);

        } catch (Exception $e) {
            Log::error('Callback processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Callback processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manual payment confirmation (for testing or admin use)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function confirmPayment(Request $request): JsonResponse
    {
        try {
            // Validate request
            $request->validate([
                'order_no' => 'required|string',
                'transaction_id' => 'required|string',
                'status' => 'required|string|in:completed,success,failed,failure,pending',
                'amount' => 'required|numeric|min:0',
                'gateway' => 'required|string'
            ]);

            // Process payment confirmation
            $result = $this->paymentOrderUpdateService->processPaymentConfirmation($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Payment confirmed successfully',
                'data' => $result
            ]);

        } catch (Exception $e) {
            Log::error('Manual payment confirmation failed', [
                'error' => $e->getMessage(),
                'payload' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment confirmation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment status for an order
     *
     * @param Request $request
     * @param string $orderNo
     * @return JsonResponse
     */
    public function getPaymentStatus(Request $request, string $orderNo): JsonResponse
    {
        try {
            // Get order details
            $order = \App\Models\Order::where('order_no', $orderNo)->first();
            
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            // Get payment transaction details
            $paymentTransaction = \Illuminate\Support\Facades\DB::table('payment_transaction')
                ->where('pre_order_id', $orderNo)
                ->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'order_no' => $order->order_no,
                    'order_status' => $order->order_status,
                    'amount' => $order->amount,
                    'amount_paid' => $order->amount_paid,
                    'payment_mode' => $order->payment_mode,
                    'payment_transaction' => $paymentTransaction ? [
                        'transaction_id' => $paymentTransaction->pk_transaction_id,
                        'gateway_transaction_id' => $paymentTransaction->gateway_transaction_id,
                        'status' => $paymentTransaction->status,
                        'gateway' => $paymentTransaction->gateway,
                        'payment_amount' => $paymentTransaction->payment_amount
                    ] : null
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get payment status', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify webhook signature (implement based on your payment gateway)
     *
     * @param Request $request
     * @return bool
     */
    protected function verifyWebhookSignature(Request $request): bool
    {
        // For testing purposes, we'll skip signature verification
        // In production, implement proper signature verification based on your payment gateway
        
        // Example for Razorpay:
        // $signature = $request->header('X-Razorpay-Signature');
        // $payload = $request->getContent();
        // $expectedSignature = hash_hmac('sha256', $payload, config('services.razorpay.webhook_secret'));
        // return hash_equals($expectedSignature, $signature);
        
        return true; // Skip verification for testing
    }

    /**
     * Simulate payment success (for testing)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function simulatePaymentSuccess(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'order_no' => 'required|string'
            ]);

            $orderNo = $request->input('order_no');
            
            // Create simulated payment data
            $paymentData = [
                'order_no' => $orderNo,
                'status' => 'completed',
                'transaction_id' => 'sim_' . uniqid(),
                'amount' => 75.00, // Default amount
                'gateway' => 'simulation',
                'payment_method' => 'test'
            ];

            // Process payment confirmation
            $result = $this->paymentOrderUpdateService->processPaymentConfirmation($paymentData);

            return response()->json([
                'success' => true,
                'message' => 'Payment simulation completed successfully',
                'data' => $result
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment simulation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simulate payment failure (for testing)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function simulatePaymentFailure(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'order_no' => 'required|string'
            ]);

            $orderNo = $request->input('order_no');
            
            // Create simulated payment data
            $paymentData = [
                'order_no' => $orderNo,
                'status' => 'failed',
                'transaction_id' => 'sim_fail_' . uniqid(),
                'amount' => 75.00,
                'gateway' => 'simulation',
                'payment_method' => 'test',
                'failure_reason' => 'Simulated payment failure'
            ];

            // Process payment confirmation
            $result = $this->paymentOrderUpdateService->processPaymentConfirmation($paymentData);

            return response()->json([
                'success' => true,
                'message' => 'Payment failure simulation completed',
                'data' => $result
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment failure simulation failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
