<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RecoverIncompleteOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:recover-incomplete {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically recover incomplete orders where payment was successful but orders were not created';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        $this->info('🔍 Scanning for incomplete orders...');
        
        // Find orders where payment is completed but orders table is empty
        $incompleteOrders = DB::select("
            SELECT
                tpo.pk_order_no,
                tpo.order_no,
                tpo.customer_name,
                tpo.amount,
                pt.status as payment_status,
                pt.gateway,
                pt.pk_transaction_id,
                top.status as temp_payment_status,
                COALESCE(o.order_count, 0) as existing_orders,
                COALESCE(od.detail_count, 0) as existing_details
            FROM temp_pre_orders tpo
            JOIN payment_transaction pt ON pt.pre_order_id = tpo.order_no
            JOIN temp_order_payment top ON top.temp_preorder_id = tpo.pk_order_no
            LEFT JOIN (
                SELECT order_no, COUNT(*) as order_count
                FROM orders
                GROUP BY order_no
            ) o ON o.order_no = tpo.order_no
            LEFT JOIN (
                SELECT ref_order_no, COUNT(*) as detail_count
                FROM order_details
                GROUP BY ref_order_no
            ) od ON od.ref_order_no = tpo.order_no
            WHERE pt.status = 'completed'
            AND (o.order_count IS NULL OR o.order_count = 0)
            AND tpo.order_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            ORDER BY tpo.pk_order_no DESC
        ");

        if (empty($incompleteOrders)) {
            $this->info('✅ No incomplete orders found.');
            return 0;
        }

        $this->info("Found " . count($incompleteOrders) . " incomplete orders:");
        
        $headers = ['Order No', 'Customer', 'Amount', 'Payment Status', 'Gateway', 'Temp Status', 'Orders', 'Details'];
        $rows = [];
        
        foreach ($incompleteOrders as $order) {
            $rows[] = [
                $order->order_no,
                $order->customer_name,
                '₹' . $order->amount,
                $order->payment_status,
                $order->gateway,
                $order->temp_payment_status,
                $order->existing_orders,
                $order->existing_details
            ];
        }
        
        $this->table($headers, $rows);

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
            return 0;
        }

        if (!$this->confirm('Do you want to recover these incomplete orders?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $recovered = 0;
        $failed = 0;

        foreach ($incompleteOrders as $order) {
            try {
                $this->info("🔧 Recovering order: {$order->order_no}");
                
                if ($this->recoverOrder($order)) {
                    $recovered++;
                    $this->info("✅ Successfully recovered: {$order->order_no}");
                } else {
                    $failed++;
                    $this->error("❌ Failed to recover: {$order->order_no}");
                }
                
            } catch (\Exception $e) {
                $failed++;
                $this->error("❌ Error recovering {$order->order_no}: " . $e->getMessage());
                Log::error('Order recovery failed', [
                    'order_no' => $order->order_no,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info("\n📊 Recovery Summary:");
        $this->info("✅ Successfully recovered: $recovered orders");
        if ($failed > 0) {
            $this->error("❌ Failed to recover: $failed orders");
        }

        return 0;
    }

    /**
     * Recover a single incomplete order
     */
    protected function recoverOrder($orderData): bool
    {
        DB::beginTransaction();
        
        try {
            // Get full temp_pre_order data
            $tempPreOrder = DB::table('temp_pre_orders')
                ->where('pk_order_no', $orderData->pk_order_no)
                ->first();
                
            if (!$tempPreOrder) {
                throw new \Exception('temp_pre_order not found');
            }

            // Parse order days
            $orderDays = json_decode($tempPreOrder->order_days, true);
            if (!$orderDays || !is_array($orderDays)) {
                $orderDays = [date('Y-m-d')]; // Default to today
            }

            $orderDate = $orderDays[0]; // Use first date (following existing pattern)

            // Create single order record (matching existing orders table structure)
            $orderId = DB::table('orders')->insertGetId([
                'company_id' => $tempPreOrder->company_id,
                'unit_id' => $tempPreOrder->unit_id,
                'fk_kitchen_code' => $tempPreOrder->fk_kitchen_code,
                'ref_order' => $tempPreOrder->pk_order_no,
                'order_no' => $tempPreOrder->order_no,
                'auth_id' => $tempPreOrder->auth_id,
                'customer_code' => $tempPreOrder->customer_code,
                'customer_name' => $tempPreOrder->customer_name,
                'food_preference' => $tempPreOrder->food_preference,
                'phone' => $tempPreOrder->phone,
                'email_address' => $tempPreOrder->email_address,
                'location_code' => $tempPreOrder->location_code,
                'location_name' => $tempPreOrder->location_name,
                'city' => $tempPreOrder->city,
                'city_name' => $tempPreOrder->city_name,
                'product_code' => $tempPreOrder->product_code,
                'product_name' => $tempPreOrder->product_name,
                'product_description' => $tempPreOrder->product_description,
                'product_type' => $tempPreOrder->product_type,
                'quantity' => $tempPreOrder->quantity,
                'product_price' => $tempPreOrder->product_price,
                'amount' => $tempPreOrder->amount,
                'applied_discount' => $tempPreOrder->applied_discount ?? 0.00,
                'amount_paid' => 1, // Mark as paid
                'tax' => $tempPreOrder->tax ?? 0.00,
                'delivery_charges' => $tempPreOrder->delivery_charges ?? 0.00,
                'service_charges' => $tempPreOrder->service_charges ?? 0.00,
                'order_status' => 'Confirmed',
                'order_date' => $orderDate,
                'due_date' => $tempPreOrder->due_date,
                'ship_address' => $tempPreOrder->ship_address,
                'order_menu' => $tempPreOrder->order_menu,
                'invoice_status' => 'Bill',
                'inventory_type' => $tempPreOrder->inventory_type,
                'food_type' => $tempPreOrder->food_type,
                'delivery_type' => $tempPreOrder->delivery_type,
                'payment_mode' => $orderData->gateway,
                'days_preference' => $tempPreOrder->days_preference,
                'source' => 'api',
                'delivery_time' => $tempPreOrder->delivery_time,
                'delivery_end_time' => $tempPreOrder->delivery_end_time,
                'recurring_status' => $tempPreOrder->recurring_status ?? '1',
                'created_date' => now()
            ]);

            // Create order details (default meal items)
            $mealItems = [
                ['product_code' => $tempPreOrder->product_code + 1, 'product_name' => 'Main Course', 'quantity' => 1, 'amount' => $tempPreOrder->amount * 0.64],
                ['product_code' => $tempPreOrder->product_code + 2, 'product_name' => 'Side Dish', 'quantity' => 1, 'amount' => $tempPreOrder->amount * 0.36],
                ['product_code' => $tempPreOrder->product_code, 'product_name' => $tempPreOrder->product_name, 'quantity' => 1, 'amount' => $tempPreOrder->amount]
            ];

            foreach ($mealItems as $item) {
                DB::table('order_details')->insert([
                    'company_id' => $tempPreOrder->company_id,
                    'unit_id' => $tempPreOrder->unit_id,
                    'ref_order_no' => $tempPreOrder->order_no,
                    'meal_code' => $item['product_code'],
                    'product_code' => $item['product_code'],
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'product_type' => 'Meal',
                    'order_date' => $orderDate,
                    'product_amount' => $item['amount'],
                    'product_tax' => 0.00
                ]);
            }

            // Update temp_order_payment status
            DB::table('temp_order_payment')
                ->where('temp_preorder_id', $tempPreOrder->pk_order_no)
                ->update(['status' => 'success']);

            // Create payment_transfered record if missing
            $existingTransfer = DB::table('payment_transfered')
                ->where('fk_transaction_id', $orderData->pk_transaction_id)
                ->exists();

            if (!$existingTransfer) {
                $transferId = 'TXF_' . time() . '_' . $tempPreOrder->order_no;
                DB::table('payment_transfered')->insert([
                    'pk_transfer_id' => $transferId,
                    'company_id' => $tempPreOrder->company_id,
                    'unit_id' => $tempPreOrder->unit_id,
                    'fk_transaction_id' => $orderData->pk_transaction_id,
                    'source' => 'customer',
                    'recipient' => 'merchant',
                    'amount' => (int)($tempPreOrder->amount * 100),
                    'currency' => 'INR',
                    'amount_reversed' => 0,
                    'transfered_at' => time(),
                    'created_date' => date('Y-m-d H:i:s'),
                    'description' => "Payment transfer for order {$tempPreOrder->order_no}"
                ]);
            }

            DB::commit();
            
            Log::info('Order recovered successfully', [
                'order_no' => $tempPreOrder->order_no,
                'order_id' => $orderId,
                'customer' => $tempPreOrder->customer_name
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
