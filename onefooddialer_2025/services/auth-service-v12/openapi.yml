openapi: 3.0.0
info:
  title: OneFoodDialer Authentication Service API
  version: '1.2.0'
  description: |
    Authentication service for OneFoodDialer platform with Keycloak integration.

    ## Important Notes
    - Username field in registration is used for Keycloak but not stored in database
    - Phone number is used as username in Keycloak for mobile compatibility
    - Admin users are stored in 'users' table, Customer users in 'customers' table
    - All authentication tokens are Keycloak JWT tokens
    - Database columns like 'password', 'username', 'auth_type' are not used as they don't exist in the current schema
servers:
  - url: 'https://api.onefooddialer.com/api/v2'
    description: Production server
  - url: 'http://localhost:8001/api/v2'
    description: Development server
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
        username:
          type: string
          nullable: true
        email:
          type: string
          format: email
        phone:
          type: string
          minLength: 10
          maxLength: 15
        first_name:
          type: string
        last_name:
          type: string
        role_id:
          type: integer
        status:
          type: integer
        company_id:
          type: integer
        unit_id:
          type: integer
        auth_type:
          type: string
          enum: [local, keycloak]
    Error:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        error:
          type: string
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "Use Laravel Sanctum token for Admin users or Keycloak JWT token for Customer users"
paths:
  /auth/register:
    post:
      summary: Register a new user
      description: Register a new user as either Admin or Customer
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - phone
                - password
                - first_name
                - last_name
                - role_name
                - company_id
                - unit_id
              properties:
                email:
                  type: string
                  format: email
                phone:
                  type: string
                  pattern: '^\d{10,15}$'
                username:
                  type: string
                  nullable: true
                  description: "Username for Keycloak (will use phone number if not provided). Note: This field is not stored in the database but used for Keycloak registration."
                password:
                  type: string
                  minLength: 8
                first_name:
                  type: string
                last_name:
                  type: string
                role_name:
                  type: string
                  enum: [Admin, Customer]
                company_id:
                  type: integer
                unit_id:
                  type: integer
                old_gate_user_id:
                  type: string
                  nullable: true
                old_sso_user_id:
                  type: string
                  nullable: true
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      token:
                        type: string
                      keycloak_registered:
                        type: boolean
                      laravel_registered:
                        type: boolean
                      auth_type:
                        type: string
                        enum: [local, keycloak]
                      keycloak_tokens:
                        type: object
                        nullable: true
                        properties:
                          access_token:
                            type: string
                            description: Keycloak JWT access token
                          refresh_token:
                            type: string
                            description: Keycloak refresh token for session management
                          expires_in:
                            type: integer
                            description: Token expiration time in seconds
                          token_type:
                            type: string
                            example: "Bearer"
                      role_based_storage:
                        type: object
                        properties:
                          table_used:
                            type: string
                            enum: [users, customers]
                            description: "Admin role users stored in 'users' table, Customer role users stored in 'customers' table"
                          keycloak_user_id:
                            type: string
                            description: "Keycloak UUID stored as auth_id for customers, or in thirdparty field"
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /auth/login:
    post:
      summary: Login user
      description: Login with email/phone and password
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: Can be email or phone number
                password:
                  type: string
                  minLength: 8
                remember:
                  type: boolean
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      token:
                        type: string
                      auth_type:
                        type: string
                        enum: [local, keycloak]
                      keycloak_tokens:
                        type: object
                        nullable: true
                        properties:
                          access_token:
                            type: string
                            description: Keycloak JWT access token for API authentication
                          refresh_token:
                            type: string
                            description: Keycloak refresh token for session management and logout
                          expires_in:
                            type: integer
                            description: Token expiration time in seconds
                          token_type:
                            type: string
                            example: "Bearer"
                      role_based_authentication:
                        type: object
                        properties:
                          role_name:
                            type: string
                            enum: [Admin, Customer]
                            description: "Admin users get Laravel Sanctum tokens, Customer users get Keycloak tokens"
                          auth_method:
                            type: string
                            enum: [sanctum, keycloak]
                            description: "Authentication method based on user role"
                          expires_in:
                            type: integer
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/logout:
    post:
      summary: Logout user
      description: Logout user from both Laravel session and Keycloak session. Handles both Admin (Sanctum) and Customer (Keycloak) tokens automatically.
      tags:
        - Authentication
      security:
        - BearerAuth: []
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                refresh_token:
                  type: string
                  description: Keycloak refresh token (required for Customer users to properly terminate Keycloak sessions)
                  nullable: true
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Logged out successfully from both Laravel and Keycloak"
                  data:
                    type: object
                    properties:
                      logout_methods:
                        type: array
                        items:
                          type: string
                        description: "Methods used for logout (e.g., sanctum, keycloak_refresh_token, keycloak_token_revoke, admin_api)"
                      session_terminated:
                        type: boolean
                        description: "Whether the session was properly terminated"
        '401':
          description: Unauthorized - Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'


