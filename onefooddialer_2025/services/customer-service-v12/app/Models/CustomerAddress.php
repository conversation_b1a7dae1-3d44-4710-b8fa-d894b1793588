<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;

/**
 * Customer Address Model
 * 
 * This model represents a customer's address in the system.
 */
class CustomerAddress extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer_address';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_address_code';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'fk_customer_code',
        'company_id',
        'unit_id',
        'menu_type',
        'city',
        'location_code',
        'location_name',
        'location_address',
        'location_zipcode',
        'delivery_person_id',
        'default',
        'dabbawala_code_type',
        'dabbawala_code',
        'dabbawala_image'
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'student_profile',
        'child_name',
        'class',
        'division',
        'floor',
        'allergies',
        'has_allergies'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_default' => 'boolean',
            'status' => 'boolean',
            'latitude' => 'float',
            'longitude' => 'float',
            'default' => 'boolean',
            'delivery_person_id' => 'integer',
            'location_code' => 'integer',
        ];
    }

    /**
     * Get the customer that owns the address.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'fk_customer_code', 'pk_customer_code');
    }

    /**
     * Get the full address as a string.
     *
     * @return string
     */
    public function getFullAddressAttribute(): string
    {
        $address = $this->address_line1;
        
        if (!empty($this->address_line2)) {
            $address .= ', ' . $this->address_line2;
        }
        
        if (!empty($this->landmark)) {
            $address .= ', ' . $this->landmark;
        }
        
        $address .= ', ' . $this->city;
        
        if (!empty($this->state)) {
            $address .= ', ' . $this->state;
        }
        
        if (!empty($this->country)) {
            $address .= ', ' . $this->country;
        }
        
        if (!empty($this->pincode)) {
            $address .= ' - ' . $this->pincode;
        }
        
        return $address;
    }

    /**
     * Scope a query to only include default addresses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope a query to only include addresses of a specific type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeType($query, $type)
    {
        return $query->where('address_type', $type);
    }

    /**
     * Scope a query to only include addresses from a specific company.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $companyId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to only include addresses from a specific unit.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    /**
     * Get the student profile information from location_address field
     * Format: "Child Name, Class, Division, Floor, Allergies"
     * Allergies are separated by " - " (e.g., "Wheat - Lactose - Nuts")
     *
     * Updated to work with all addresses that have comma-separated format,
     * not just those with menu_type = 'school'
     */
    public function getStudentProfileAttribute()
    {
        // Check if this is a company_id=8163 address with comma-separated format
        if ($this->company_id != 8163 || empty($this->location_address) ||
            strpos($this->location_address, ',') === false) {
            return null;
        }

        $parts = array_map('trim', explode(',', $this->location_address));

        // Ensure we have at least 4 parts for a valid student profile
        if (count($parts) < 4) {
            return null;
        }

        $profile = [
            'child_name' => $parts[0] ?? '',
            'class' => $parts[1] ?? '',
            'division' => $parts[2] ?? '',
            'floor' => $parts[3] ?? '',
            'allergies' => [],
            'raw_allergies' => ''
        ];

        // Parse allergies if they exist (5th part)
        if (isset($parts[4]) && !empty(trim($parts[4]))) {
            $allergiesString = trim($parts[4]);
            $profile['raw_allergies'] = $allergiesString;

            // Handle different allergy separators
            if (strpos($allergiesString, ' - ') !== false) {
                $profile['allergies'] = array_map('trim', explode(' - ', $allergiesString));
            } elseif (strpos($allergiesString, ',') !== false) {
                $profile['allergies'] = array_map('trim', explode(',', $allergiesString));
            } else {
                // If no separator found, treat as a single allergy
                $profile['allergies'] = [$allergiesString];
            }

            // Filter out empty allergies
            $profile['allergies'] = array_filter($profile['allergies']);
        }

        // Only return profile if we have meaningful data
        if (empty($profile['child_name']) && empty($profile['class']) &&
            empty($profile['division']) && empty($profile['floor'])) {
            return null;
        }

        return $profile;
    }

    /**
     * Get the child's name from location_address (for school addresses)
     */
    public function getChildNameAttribute()
    {
        $profile = $this->student_profile;
        return $profile ? $profile['child_name'] : null;
    }

    /**
     * Get the child's class from location_address (for school addresses)
     */
    public function getClassAttribute()
    {
        $profile = $this->student_profile;
        return $profile ? $profile['class'] : null;
    }

    /**
     * Get the child's division from location_address (for school addresses)
     */
    public function getDivisionAttribute()
    {
        $profile = $this->student_profile;
        return $profile ? $profile['division'] : null;
    }

    /**
     * Get the floor from location_address (for school addresses)
     */
    public function getFloorAttribute()
    {
        $profile = $this->student_profile;
        return $profile ? $profile['floor'] : null;
    }

    /**
     * Get allergies array from location_address (for school addresses)
     */
    public function getAllergiesAttribute()
    {
        $profile = $this->student_profile;
        return $profile ? $profile['allergies'] : [];
    }

    /**
     * Check if student has allergies
     */
    public function getHasAllergiesAttribute()
    {
        $allergies = $this->allergies;
        return !empty($allergies) && !empty($allergies[0]);
    }

    /**
     * Set location_address from student profile components
     */
    public function setLocationAddressFromComponents(array $components)
    {
        $parts = [
            $components['child_name'] ?? '',
            $components['class'] ?? '',
            $components['division'] ?? '',
            $components['floor'] ?? '',
        ];

        // Add allergies if provided
        if (!empty($components['allergies']) && is_array($components['allergies'])) {
            $parts[] = implode(' - ', $components['allergies']);
        }

        $this->location_address = implode(', ', array_filter($parts));
    }

    /**
     * Scope to filter by customer (for fk_customer_code field)
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('fk_customer_code', $customerId);
    }

    /**
     * Scope to filter by menu type (for school addresses)
     */
    public function scopeByMenuType($query, $menuType)
    {
        return $query->where('menu_type', $menuType);
    }

    /**
     * Scope to filter by location code (for school addresses)
     */
    public function scopeByLocation($query, $locationCode)
    {
        return $query->where('location_code', $locationCode);
    }

    /**
     * Scope to search across multiple fields
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('address_name', 'LIKE', "%{$search}%")
              ->orWhere('location_name', 'LIKE', "%{$search}%")
              ->orWhere('location_address', 'LIKE', "%{$search}%")
              ->orWhere('address_line1', 'LIKE', "%{$search}%")
              ->orWhere('address_line2', 'LIKE', "%{$search}%");
        });
    }

    /**
     * Scope for student addresses with allergies
     */
    public function scopeWithAllergies($query)
    {
        return $query->where('menu_type', 'school')
                    ->where('location_address', 'LIKE', '%,%,%,%,%');
    }

    /**
     * Get the delivery location relationship (for school addresses)
     */
    public function deliveryLocation()
    {
        return $this->belongsTo(DeliveryLocation::class, 'location_code', 'pk_location_code');
    }
}
