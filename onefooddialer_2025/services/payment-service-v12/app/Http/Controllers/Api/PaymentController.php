<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Payment\InitiatePaymentRequest;
use App\Http\Requests\Payment\ProcessPaymentRequest;
use App\Http\Requests\Payment\RefundPaymentRequest;
// use App\Services\PaymentLogService;
use App\Services\PaymentMethodService;
use App\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    /**
     * Display a listing of the payment transactions.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $paginate = $request->input('per_page') ?? 10;
            $payments = \App\Models\PaymentTransaction::orderByDesc('created_date')->paginate($paginate);
            return response()->json([
                'success' => true,
                'data' => $payments->items(),
                'meta' => [
                    'current_page' => $payments->currentPage(),
                    'per_page' => $payments->perPage(),
                    'total' => $payments->total(),
                    'last_page' => $payments->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to fetch payment transactions', [
                'error' => $e->getMessage()
            ]);
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * The payment service.
     *
     * @var PaymentService
     */
    protected $paymentService;

    /**
     * The payment method service.
     *
     * @var PaymentMethodService
     */
    protected $paymentMethodService;

    /**
     * The payment log service.
     *
     * @var PaymentLogService
     */
    protected $paymentLogService;

    /**
     * Create a new controller instance.
     *
     * @param PaymentService $paymentService
     * @param PaymentMethodService $paymentMethodService
     * @param PaymentLogService $paymentLogService
     * @return void
     */
    public function __construct(
        PaymentService $paymentService,
        PaymentMethodService $paymentMethodService
    ) {
        $this->paymentService = $paymentService;
        $this->paymentMethodService = $paymentMethodService;
        // $this->paymentLogService = $paymentLogService;
    }

    /**
     * Initiate a payment.
     *
     * @param InitiatePaymentRequest $request
     * @return JsonResponse
     */
    public function initiate(InitiatePaymentRequest $request): JsonResponse
    {
        try {
            $transaction = $this->paymentService->initiatePayment($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Payment initiated successfully',
                'data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'amount' => $transaction->amount,
                    'status' => $transaction->status
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('Payment initiation failed', [
                'error' => $e->getMessage(),
                'request' => $request->validated()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process a payment.
     *
     * @param ProcessPaymentRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function process(ProcessPaymentRequest $request, string $id): JsonResponse
    {
        try {
            $formData = $this->paymentService->processPayment(
                $id,
                $request->validated()['gateway']
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment processing initiated',
                'data' => $formData
            ]);
        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $id,
                'gateway' => $request->validated()['gateway']
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle a payment callback.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function callback(Request $request): JsonResponse
    {
        try {
            $transaction = $this->paymentService->handleCallback($request->all(), $request);

            return response()->json([
                'success' => true,
                'message' => 'Payment callback processed',
                'data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'status' => $transaction->status,
                    'gateway_transaction_id' => $transaction->gateway_transaction_id
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Payment callback handling failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a payment status.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function status(string $id): JsonResponse
    {
        try {
            $transaction = $this->paymentService->getTransaction($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->transaction_id,
                    'amount' => $transaction->amount,
                    'status' => $transaction->status,
                    'gateway' => $transaction->gateway,
                    'gateway_transaction_id' => $transaction->gateway_transaction_id,
                    'created_at' => $transaction->created_at,
                    'updated_at' => $transaction->updated_at
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Payment status retrieval failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Refund a payment.
     *
     * @param RefundPaymentRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function refund(RefundPaymentRequest $request, string $id): JsonResponse
    {
        try {
            $result = $this->paymentService->refundPayment(
                $id,
                $request->validated()['amount'] ?? null
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment refunded successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $id,
                'amount' => $request->validated()['amount'] ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->paymentService->getTransactionStatistics($request->all());

            return response()->json([
                'success' => true,
                'data' => $statistics
            ]);
        } catch (\Exception $e) {
            Log::error('Payment statistics retrieval failed', [
                'error' => $e->getMessage(),
                'filters' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment logs.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function logs(Request $request): JsonResponse
    {
        try {
            $criteria = $request->only([
                'transaction_id',
                'gateway',
                'event',
                'status',
                'start_date',
                'end_date'
            ]);

            // $logs = $this->paymentLogService->searchLogs($criteria, $request->input('limit', 50));

            return response()->json([
                'success' => true,
                'data' => $logs
            ]);
        } catch (\Exception $e) {
            Log::error('Payment logs retrieval failed', [
                'error' => $e->getMessage(),
                'criteria' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment logs for a transaction.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function transactionLogs(string $id): JsonResponse
    {
        try {
            // $logs = $this->paymentLogService->getLogsForTransaction($id);

            return response()->json([
                'success' => true,
                'data' => $logs
            ]);
        } catch (\Exception $e) {
            Log::error('Transaction logs retrieval failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle a payment webhook.
     *
     * @param Request $request
     * @param string $gateway
     * @return JsonResponse
     */
    public function webhook(Request $request, string $gateway): JsonResponse
    {
        try {
            // Log the webhook request
            // $this->paymentLogService->logEvent(
            //     'webhook',
            //     'received',
            //     [],
            //     $request->all(),
            //     null,
            //     $gateway,
            //     $request
            // );

            Log::info('Payment webhook received', [
                'webhook',
                'received',
                [],
                $request->all(),
                null,
                $gateway,
                $request
            ]);

            // Process the webhook based on the gateway
            $transaction = $this->paymentService->handleCallback($request->all(), $request);

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Payment webhook handling failed', [
                'error' => $e->getMessage(),
                'gateway' => $gateway,
                'request' => $request->all()
            ]);

            // Log the error
            // $this->paymentLogService->logEvent(
            //     'webhook',
            //     'error',
            //     [],
            //     [
            //         'error' => $e->getMessage(),
            //         'gateway' => $gateway
            //     ],
            //     null,
            //     $gateway,
            //     $request
            // );

            Log::error('Payment webhook error', [
                    'webhook',
                'error',
                [],
                [
                    'error' => $e->getMessage(),
                    'gateway' => $gateway
                ],
                null,
                $gateway,
                $request
            ]);

            // Return a 200 response to prevent retries
            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed, but acknowledged'
            ], 200);
        }
    }

    /**
     * Test PhonePe payment integration with dummy data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testPhonePe(Request $request): JsonResponse
    {
        try {
            // Use dummy data for testing
            $testData = [
                'customer_id' => $request->input('customer_id', 12345),
                'customer_name' => $request->input('customer_name', 'John Doe'),
                'customer_email' => $request->input('customer_email', '<EMAIL>'),
                'customer_phone' => $request->input('customer_phone', '9876543210'),
                'amount' => $request->input('amount', 299.99),
                'order_id' => $request->input('order_id', 'ORDER_TEST_' . uniqid()),
                'success_url' => $request->input('success_url', 'https://example.com/success'),
                'failure_url' => $request->input('failure_url', 'https://example.com/failure'),
                'context' => $request->input('context', 'order'),
                'referer' => $request->input('referer', 'website'),
                'redirect_url' => $request->input('redirect_url', 'https://example.com/redirect'),
                'callback_url' => $request->input('callback_url', 'https://example.com/callback'),
            ];

            // Initialize PhonePe gateway with test configuration
            $phonePeGateway = new \App\Services\Payment\Gateway\PhonePeGateway([
                'merchant_id' => config('payment.gateways.phonepe.merchant_id', 'UATMERCHANT'),
                'salt_key' => config('payment.gateways.phonepe.salt_key', '8289e078-be0b-484d-ae60-052f117f8deb'),
                'salt_index' => config('payment.gateways.phonepe.salt_index', '1'),
                'base_url' => config('payment.gateways.phonepe.test_url', 'https://api-preprod.phonepe.com/apis/pg-sandbox'),
            ]);

            // For testing purposes, simulate a successful PhonePe response
            $mockResponse = [
                'redirect_url' => 'https://mercury-uat.phonepe.com/transact/pg?token=test_token_' . uniqid(),
                'payment_gateway' => 'phonepe',
                'order_id' => $testData['order_id'],
                'transaction_id' => 'TXN_' . uniqid(),
                'status' => 'initiated',
                'message' => 'Payment initiated successfully'
            ];

            // Try to generate actual payment form, but fallback to mock on error
            try {
                $paymentResponse = $phonePeGateway->generatePaymentForm($testData);
            } catch (\Exception $e) {
                Log::info('PhonePe API call failed, using mock response for testing', [
                    'error' => $e->getMessage(),
                    'test_data' => $testData
                ]);
                $paymentResponse = $mockResponse;
            }

            return response()->json([
                'success' => true,
                'message' => 'PhonePe payment test initiated successfully',
                'data' => [
                    'test_data' => $testData,
                    'phonepe_response' => $paymentResponse,
                    'gateway_config' => [
                        'merchant_id' => config('payment.gateways.phonepe.merchant_id'),
                        'base_url' => config('payment.gateways.phonepe.test_url'),
                        'mode' => config('payment.gateways.phonepe.mode', 'test'),
                    ]
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('PhonePe test payment failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'PhonePe test payment failed: ' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }

    /**
     * Test PhonePe payment verification with dummy data
     *
     * @param Request $request
     * @param string $transactionId
     * @return JsonResponse
     */
    public function testPhonePeVerify(Request $request, string $transactionId): JsonResponse
    {
        try {
            // Initialize PhonePe gateway with test configuration
            $phonePeGateway = new \App\Services\Payment\Gateway\PhonePeGateway([
                'merchant_id' => config('payment.gateways.phonepe.merchant_id', 'UATMERCHANT'),
                'salt_key' => config('payment.gateways.phonepe.salt_key', '8289e078-be0b-484d-ae60-052f117f8deb'),
                'salt_index' => config('payment.gateways.phonepe.salt_index', '1'),
                'base_url' => config('payment.gateways.phonepe.test_url', 'https://api-preprod.phonepe.com/apis/pg-sandbox'),
            ]);

            // Mock verification response for testing
            $mockVerificationResponse = [
                'transaction_id' => $transactionId,
                'status' => 'SUCCESS',
                'amount' => 29999, // in paise
                'merchant_transaction_id' => $transactionId,
                'payment_instrument' => [
                    'type' => 'UPI',
                    'utr' => 'UTR' . uniqid()
                ],
                'response_code' => 'SUCCESS',
                'message' => 'Payment completed successfully'
            ];

            // Try to verify actual payment, but fallback to mock on error
            try {
                $verificationResponse = $phonePeGateway->verifyPayment($transactionId);
            } catch (\Exception $e) {
                Log::info('PhonePe verification API call failed, using mock response for testing', [
                    'transaction_id' => $transactionId,
                    'error' => $e->getMessage()
                ]);
                $verificationResponse = $mockVerificationResponse;
            }

            return response()->json([
                'success' => true,
                'message' => 'PhonePe payment verification test completed',
                'data' => [
                    'transaction_id' => $transactionId,
                    'verification_response' => $verificationResponse,
                    'gateway_config' => [
                        'merchant_id' => config('payment.gateways.phonepe.merchant_id'),
                        'base_url' => config('payment.gateways.phonepe.test_url'),
                        'mode' => config('payment.gateways.phonepe.mode', 'test'),
                    ]
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('PhonePe test verification failed', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'PhonePe test verification failed: ' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }

    /**
     * Test PhonePe payment status check with dummy data
     *
     * @param Request $request
     * @param string $transactionId
     * @return JsonResponse
     */
    public function testPhonePeStatus(Request $request, string $transactionId): JsonResponse
    {
        try {
            // Initialize PhonePe gateway with test configuration
            $phonePeGateway = new \App\Services\Payment\Gateway\PhonePeGateway([
                'merchant_id' => config('payment.gateways.phonepe.merchant_id', 'UATMERCHANT'),
                'salt_key' => config('payment.gateways.phonepe.salt_key', '8289e078-be0b-484d-ae60-052f117f8deb'),
                'salt_index' => config('payment.gateways.phonepe.salt_index', '1'),
                'base_url' => config('payment.gateways.phonepe.test_url', 'https://api-preprod.phonepe.com/apis/pg-sandbox'),
            ]);

            // Mock status response for testing
            $mockStatusResponse = [
                'transaction_id' => $transactionId,
                'status' => 'PENDING',
                'amount' => 29999, // in paise
                'merchant_transaction_id' => $transactionId,
                'created_at' => now()->toISOString(),
                'updated_at' => now()->toISOString(),
                'response_code' => 'PENDING',
                'message' => 'Payment is being processed'
            ];

            // Try to get actual payment status, but fallback to mock on error
            try {
                $statusResponse = $phonePeGateway->getPaymentStatus($transactionId);
            } catch (\Exception $e) {
                Log::info('PhonePe status API call failed, using mock response for testing', [
                    'transaction_id' => $transactionId,
                    'error' => $e->getMessage()
                ]);
                $statusResponse = $mockStatusResponse;
            }

            return response()->json([
                'success' => true,
                'message' => 'PhonePe payment status test completed',
                'data' => [
                    'transaction_id' => $transactionId,
                    'status_response' => $statusResponse,
                    'gateway_config' => [
                        'merchant_id' => config('payment.gateways.phonepe.merchant_id'),
                        'base_url' => config('payment.gateways.phonepe.test_url'),
                        'mode' => config('payment.gateways.phonepe.mode', 'test'),
                    ]
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('PhonePe test status check failed', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'PhonePe test status check failed: ' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }

    /**
     * Test Razorpay payment initiation with dummy data
     */
    public function testRazorpay(Request $request): JsonResponse
    {
        try {
            // Use dummy test data
            $testData = [
                'customer_id' => 12345,
                'customer_name' => 'John Doe',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '9876543210',
                'amount' => 299.99,
                'order_id' => 'ORDER_TEST_' . uniqid()
            ];

            Log::info('Razorpay Test Payment Initiation', [
                'test_data' => $testData
            ]);

            // Initialize Razorpay gateway
            $razorpayGateway = new \App\Services\Payment\Gateway\RazorpayGateway([
                'key_id' => env('RAZORPAY_KEY_ID', 'rzp_test_ZUpflviU0kpnf0'),
                'key_secret' => env('RAZORPAY_KEY_SECRET', 'vaVSn1chhm19HW809rG0X8HS')
            ]);

            // Generate payment form data
            $paymentData = [
                'order_id' => $testData['order_id'],
                'amount' => $testData['amount'],
                'customer_name' => $testData['customer_name'],
                'customer_email' => $testData['customer_email'],
                'customer_phone' => $testData['customer_phone'],
                'description' => 'Test Payment for Razorpay Integration',
                'callback_url' => url('/api/v2/payments/callback')
            ];

            $razorpayResponse = $razorpayGateway->generatePaymentForm($paymentData);

            Log::info('Razorpay Test Payment Response', [
                'order_id' => $testData['order_id'],
                'razorpay_response' => $razorpayResponse
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Razorpay payment test initiated successfully',
                'data' => [
                    'test_data' => $testData,
                    'razorpay_response' => $razorpayResponse,
                    'frontend_integration' => [
                        'checkout_url' => 'https://checkout.razorpay.com/v1/checkout.js',
                        'options' => [
                            'key' => $razorpayResponse['razorpay_key'],
                            'amount' => $razorpayResponse['amount'],
                            'currency' => $razorpayResponse['currency'],
                            'name' => 'OneFoodDialer',
                            'description' => $razorpayResponse['description'],
                            'order_id' => $razorpayResponse['order_id'],
                            'prefill' => [
                                'name' => $razorpayResponse['name'],
                                'email' => $razorpayResponse['email'],
                                'contact' => $razorpayResponse['contact']
                            ]
                        ]
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Razorpay Test Payment Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Razorpay test payment failed: ' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }

    /**
     * Test Razorpay payment verification
     */
    public function testRazorpayVerify(Request $request, string $paymentId): JsonResponse
    {
        try {
            Log::info('Razorpay Test Payment Verification', [
                'payment_id' => $paymentId
            ]);

            // Initialize Razorpay gateway
            $razorpayGateway = new \App\Services\Payment\Gateway\RazorpayGateway([
                'key_id' => env('RAZORPAY_KEY_ID', 'rzp_test_ZUpflviU0kpnf0'),
                'key_secret' => env('RAZORPAY_KEY_SECRET', 'vaVSn1chhm19HW809rG0X8HS')
            ]);

            // Verify payment
            $verificationResponse = $razorpayGateway->verifyPayment($paymentId);

            Log::info('Razorpay Test Payment Verification Response', [
                'payment_id' => $paymentId,
                'verification_response' => $verificationResponse
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Razorpay payment verification test completed',
                'data' => [
                    'payment_id' => $paymentId,
                    'verification_response' => $verificationResponse
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Razorpay Test Payment Verification Exception', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Razorpay test verification failed: ' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }

    /**
     * Test Razorpay payment status check
     */
    public function testRazorpayStatus(Request $request, string $paymentId): JsonResponse
    {
        try {
            Log::info('Razorpay Test Payment Status Check', [
                'payment_id' => $paymentId
            ]);

            // Initialize Razorpay gateway
            $razorpayGateway = new \App\Services\Payment\Gateway\RazorpayGateway([
                'key_id' => env('RAZORPAY_KEY_ID', 'rzp_test_ZUpflviU0kpnf0'),
                'key_secret' => env('RAZORPAY_KEY_SECRET', 'vaVSn1chhm19HW809rG0X8HS')
            ]);

            // Get payment status
            $statusResponse = $razorpayGateway->getPaymentStatus($paymentId);

            Log::info('Razorpay Test Payment Status Response', [
                'payment_id' => $paymentId,
                'status_response' => $statusResponse
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Razorpay payment status test completed',
                'data' => [
                    'payment_id' => $paymentId,
                    'status_response' => $statusResponse
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Razorpay Test Payment Status Exception', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Razorpay test status check failed: ' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }

    /**
     * Test complete Razorpay payment flow (initiate + process)
     */
    public function testRazorpayFlow(Request $request): JsonResponse
    {
        try {
            // Use dummy test data
            $testData = [
                'customer_id' => 12345,
                'customer_name' => 'John Doe',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '9876543210',
                'amount' => 299.99,
                'order_id' => 'ORDER_TEST_' . uniqid()
            ];

            Log::info('Razorpay Complete Flow Test Initiation', [
                'test_data' => $testData
            ]);

            // Step 1: Initiate payment transaction
            $transaction = $this->paymentService->initiatePayment([
                'customer_id' => $testData['customer_id'],
                'customer_name' => $testData['customer_name'],
                'customer_email' => $testData['customer_email'],
                'customer_phone' => $testData['customer_phone'],
                'amount' => $testData['amount'],
                'transaction_charges' => 0,
                'wallet_amount' => 0,
                'order_id' => $testData['order_id'],
                'referer' => 'mobile_app',
                'success_url' => 'https://yourapp.com/payment/success',
                'failure_url' => 'https://yourapp.com/payment/failure',
                'context' => 'order_payment',
                'recurring' => false,
                'discount' => 0
            ]);

            Log::info('Payment transaction initiated', [
                'transaction_id' => $transaction->transaction_id,
                'status' => $transaction->status
            ]);

            // Step 2: Process payment with Razorpay gateway
            $formData = $this->paymentService->processPayment(
                $transaction->transaction_id,
                'razorpay'
            );

            Log::info('Razorpay payment processed', [
                'transaction_id' => $transaction->transaction_id,
                'form_data' => $formData
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Razorpay complete payment flow test completed successfully',
                'data' => [
                    'step_1_initiate' => [
                        'transaction_id' => $transaction->transaction_id,
                        'amount' => $transaction->amount,
                        'status' => $transaction->status,
                        'customer_details' => [
                            'id' => $testData['customer_id'],
                            'name' => $testData['customer_name'],
                            'email' => $testData['customer_email'],
                            'phone' => $testData['customer_phone']
                        ]
                    ],
                    'step_2_process' => [
                        'gateway' => 'razorpay',
                        'razorpay_response' => $formData
                    ],
                    'frontend_integration' => [
                        'checkout_url' => 'https://checkout.razorpay.com/v1/checkout.js',
                        'options' => [
                            'key' => $formData['razorpay_key'] ?? null,
                            'amount' => $formData['amount'] ?? null,
                            'currency' => $formData['currency'] ?? 'INR',
                            'name' => 'OneFoodDialer',
                            'description' => $formData['description'] ?? 'Order Payment',
                            'order_id' => $formData['order_id'] ?? null,
                            'prefill' => [
                                'name' => $formData['name'] ?? $testData['customer_name'],
                                'email' => $formData['email'] ?? $testData['customer_email'],
                                'contact' => $formData['contact'] ?? $testData['customer_phone']
                            ]
                        ]
                    ],
                    'next_steps' => [
                        'integrate_frontend' => 'Use the razorpay_response data to integrate with Razorpay Checkout',
                        'handle_callback' => 'Handle payment success/failure in your app',
                        'verify_payment' => 'Verify payment signature on callback',
                        'update_order' => 'Update order status in database after successful payment'
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Razorpay Complete Flow Test Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Razorpay complete flow test failed: ' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }

    /**
     * Debug gateway registration
     */
    public function debugGateways(Request $request): JsonResponse
    {
        try {
            // Get available gateways from PaymentService
            $reflection = new \ReflectionClass($this->paymentService);
            $gatewaysProperty = $reflection->getProperty('gateways');
            $gatewaysProperty->setAccessible(true);
            $gateways = $gatewaysProperty->getValue($this->paymentService);

            $gatewayList = [];
            foreach ($gateways as $name => $gateway) {
                $gatewayList[$name] = [
                    'name' => $gateway->getName(),
                    'enabled' => $gateway->isEnabled(),
                    'class' => get_class($gateway)
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Gateway registration debug info',
                'data' => [
                    'registered_gateways' => $gatewayList,
                    'total_gateways' => count($gatewayList),
                    'razorpay_registered' => isset($gateways['razorpay']),
                    'service_class' => get_class($this->paymentService)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Debug failed: ' . $e->getMessage(),
                'error_details' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ]
            ], 500);
        }
    }

    /**
     * Handle Razorpay webhook notifications
     * This is called automatically by Razorpay when payment events occur
     */
    public function razorpayWebhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->getContent();
            $signature = $request->header('X-Razorpay-Signature');

            Log::info('Razorpay Webhook Received', [
                'signature' => $signature,
                'payload' => $payload
            ]);

            // Verify webhook signature (optional but recommended)
            // $webhookSecret = env('RAZORPAY_WEBHOOK_SECRET');
            // if ($webhookSecret) {
            //     $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);
            //     if (!hash_equals($signature, $expectedSignature)) {
            //         return response()->json(['error' => 'Invalid signature'], 400);
            //     }
            // }

            $data = json_decode($payload, true);
            $event = $data['event'] ?? '';
            $paymentData = $data['payload']['payment']['entity'] ?? [];

            Log::info('Razorpay Webhook Event', [
                'event' => $event,
                'payment_id' => $paymentData['id'] ?? null,
                'order_id' => $paymentData['order_id'] ?? null,
                'status' => $paymentData['status'] ?? null
            ]);

            // Handle different webhook events
            switch ($event) {
                case 'payment.captured':
                    return $this->handlePaymentCaptured($paymentData);
                case 'payment.failed':
                    return $this->handlePaymentFailed($paymentData);
                default:
                    Log::info('Unhandled webhook event', ['event' => $event]);
                    return response()->json(['message' => 'Event received'], 200);
            }

        } catch (\Exception $e) {
            Log::error('Razorpay Webhook Error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Handle payment.captured webhook event
     */
    private function handlePaymentCaptured(array $paymentData): JsonResponse
    {
        try {
            $razorpayOrderId = $paymentData['order_id'] ?? null;
            $razorpayPaymentId = $paymentData['id'] ?? null;

            if (!$razorpayOrderId || !$razorpayPaymentId) {
                return response()->json(['error' => 'Missing payment data'], 400);
            }

            // Find transaction by Razorpay order ID
            $transaction = PaymentTransaction::where('gateway_transaction_id', $razorpayOrderId)->first();

            if (!$transaction) {
                Log::warning('Transaction not found for webhook', [
                    'razorpay_order_id' => $razorpayOrderId,
                    'razorpay_payment_id' => $razorpayPaymentId
                ]);
                return response()->json(['error' => 'Transaction not found'], 404);
            }

            // Update transaction status
            $transaction->status = 'completed';
            $transaction->gateway_transaction_id = $razorpayPaymentId;
            $transaction->save();

            Log::info('Payment captured via webhook', [
                'transaction_id' => $transaction->transaction_id,
                'razorpay_payment_id' => $razorpayPaymentId
            ]);

            return response()->json(['message' => 'Payment captured'], 200);

        } catch (\Exception $e) {
            Log::error('Payment captured webhook error', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData
            ]);

            return response()->json(['error' => 'Processing failed'], 500);
        }
    }

    /**
     * Handle payment.failed webhook event
     */
    private function handlePaymentFailed(array $paymentData): JsonResponse
    {
        try {
            $razorpayOrderId = $paymentData['order_id'] ?? null;
            $razorpayPaymentId = $paymentData['id'] ?? null;

            if (!$razorpayOrderId) {
                return response()->json(['error' => 'Missing order ID'], 400);
            }

            // Find transaction by Razorpay order ID
            $transaction = PaymentTransaction::where('gateway_transaction_id', $razorpayOrderId)->first();

            if (!$transaction) {
                Log::warning('Transaction not found for failed payment webhook', [
                    'razorpay_order_id' => $razorpayOrderId,
                    'razorpay_payment_id' => $razorpayPaymentId
                ]);
                return response()->json(['error' => 'Transaction not found'], 404);
            }

            // Update transaction status
            $transaction->status = 'failed';
            if ($razorpayPaymentId) {
                $transaction->gateway_transaction_id = $razorpayPaymentId;
            }
            $transaction->save();

            Log::info('Payment failed via webhook', [
                'transaction_id' => $transaction->transaction_id,
                'razorpay_payment_id' => $razorpayPaymentId
            ]);

            return response()->json(['message' => 'Payment failure recorded'], 200);

        } catch (\Exception $e) {
            Log::error('Payment failed webhook error', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData
            ]);

            return response()->json(['error' => 'Processing failed'], 500);
        }
    }
}
